"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { cn } from "@/lib/utils"
import {
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  X,
  Music2,
  Waves
} from "lucide-react"
import { usePlaylistAudio, PlaylistTrack } from "@/hooks/use-playlist-audio"
import { useEffect, useState } from "react"

interface PlaylistAudioPlayerProps {
  tracks: PlaylistTrack[]
  className?: string
  onClose?: () => void
}

export function PlaylistAudioPlayer({ tracks, className, onClose }: PlaylistAudioPlayerProps) {
  const {
    currentTrack,
    isPlaying,
    volume,
    isMuted,
    currentTime,
    duration,
    currentIndex,
    setPlaylist,
    togglePlayPause,
    skipToNext,
    skipToPrevious,
    setVolume,
    toggleMute,
    seek,
    stop,
  } = usePlaylistAudio()

  const [isSeeking, setIsSeeking] = useState(false)
  const [seekTime, setSeekTime] = useState(0)

  // Set playlist when tracks change
  useEffect(() => {
    setPlaylist(tracks)
  }, [tracks, setPlaylist])

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const handleSeek = (value: number[]) => {
    const newTime = value[0]
    setSeekTime(newTime)
    setIsSeeking(true)
    seek(newTime)

    // Reset seeking state after a short delay
    setTimeout(() => {
      setIsSeeking(false)
    }, 100)
  }

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0])
  }

  const handleClose = () => {
    stop()
    onClose?.()
  }

  // Don't render if no current track
  if (!currentTrack) {
    return null
  }

  const canSkipPrevious = currentIndex > 0
  const canSkipNext = currentIndex < tracks.length - 1

  return (
    <div className={cn(
      "fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-t border-border/50 p-4 shadow-lg",
      className
    )} style={{ position: 'fixed' }}>
      <div className="max-w-[90rem] mx-auto">
        <div className="flex items-center gap-4">
          {/* Track Info */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/40 dark:to-rose-900/40 flex items-center justify-center shrink-0">
              {currentTrack.type === 'music' ? (
                <Music2 className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              ) : (
                <Waves className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
              )}
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm truncate">
                {currentTrack.title}
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{formatTime(currentTime)}</span>
                <span>/</span>
                <span>{formatTime(duration)}</span>
                {currentTrack.genres && currentTrack.genres.length > 0 && (
                  <>
                    <span>•</span>
                    <span className="truncate">{currentTrack.genres[0]}</span>
                  </>
                )}
                {currentTrack.category && currentTrack.category.length > 0 && (
                  <>
                    <span>•</span>
                    <span className="truncate">{currentTrack.category[0]}</span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Player Controls */}
          <div className="flex items-center gap-2">
            {/* Previous Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={skipToPrevious}
              disabled={!canSkipPrevious}
              className="h-8 w-8"
            >
              <SkipBack className="h-4 w-4" />
            </Button>

            {/* Play/Pause Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={togglePlayPause}
              className={cn(
                "h-10 w-10 rounded-full transition-all duration-200",
                isPlaying
                  ? "bg-orange-500 text-white hover:bg-orange-600"
                  : "hover:bg-orange-100 dark:hover:bg-orange-900/50"
              )}
            >
              {isPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5 ml-0.5" />
              )}
            </Button>

            {/* Next Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={skipToNext}
              disabled={!canSkipNext}
              className="h-8 w-8"
            >
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress Bar */}
          <div className="hidden md:flex flex-1 max-w-xs items-center">
            <Slider
              value={[isSeeking ? seekTime : currentTime]}
              min={0}
              max={duration || 100}
              step={0.1}
              onValueChange={handleSeek}
              className="cursor-pointer"
            />
          </div>

          {/* Volume Controls */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={toggleMute}
            >
              {isMuted ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
            <Slider
              value={[isMuted ? 0 : volume]}
              min={0}
              max={100}
              step={1}
              onValueChange={handleVolumeChange}
              className="w-20"
            />
          </div>

          {/* Close Button */}
          {onClose && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Mobile Progress Bar */}
        <div className="md:hidden mt-3">
          <Slider
            value={[isSeeking ? seekTime : currentTime]}
            min={0}
            max={duration || 100}
            step={0.1}
            onValueChange={handleSeek}
            className="cursor-pointer"
          />
        </div>

        {/* Track Counter */}
        <div className="absolute top-2 right-4 text-xs text-muted-foreground">
          {currentIndex + 1} / {tracks.length}
        </div>
      </div>
    </div>
  )
}
