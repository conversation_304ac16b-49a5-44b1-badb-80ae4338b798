"use client"

import { Download, Filter, User, LogOut, <PERSON><PERSON><PERSON>, Crown, Menu } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar"
import { useUserStore } from "@/store/userStore"
import { signOut } from "@/server/auth/auth-client"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

interface StatsNavbarProps {
  title: string
  subtitle?: string
  showFilters?: boolean
  showDownload?: boolean
  onProfileClick?: () => void
}

export function StatsNavbar({
  title,
  subtitle,
  showFilters = true,
  showDownload = true,
  onProfileClick
}: StatsNavbarProps) {
  const { state, isMobile } = useSidebar()
  const isMobileDevice = useIsMobile()
  const {
    user,
    isAuthenticated,
    getDisplayName,
    getAvatarUrl,
    isPremium,
    isAdmin,
    clearUser
  } = useUserStore()

  const handleSignOut = async () => {
    try {
      await signOut({
        fetchOptions: {
          onSuccess: () => {
            clearUser()
            window.location.href = "/"
          },
          onError: (ctx) => {
            console.error("Sign out error:", ctx.error)
            // Still clear local state and redirect on error
            clearUser()
            window.location.href = "/"
          }
        }
      })
    } catch (error) {
      console.error("Sign out failed:", error)
      // Fallback: clear local state and redirect
      clearUser()
      window.location.href = "/"
    }
  }

  const handleProfileClick = () => {
    if (onProfileClick) {
      onProfileClick()
    }
  }

  return (
    <div className="sticky top-0 z-50 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center justify-between px-4">
        {/* Left side - Menu Button + Title */}
        <div className="flex items-center flex-1 gap-3">
          {/* Show menu button on mobile or when sidebar is collapsed on desktop */}
          {(isMobileDevice || state === "collapsed") && (
            <SidebarTrigger
              className={cn(
                "text-muted-foreground hover:text-foreground transition-colors",
                isMobileDevice
                  ? "h-9 w-9 touch-manipulation sidebar-mobile-trigger" // Larger touch target for mobile
                  : "h-8 w-8"
              )}
            />
          )}
          <div>
            <h1 className={cn(
              "font-bold bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 bg-clip-text text-transparent",
              isMobileDevice ? "text-lg" : "text-xl"
            )}>
              {title}
            </h1>
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
        </div>

        {/* Right side - Actions and User */}
        <div className="flex items-center gap-3">


          {/* User Menu */}
          {isAuthenticated && user ? (
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                  <Avatar className="h-9 w-9">
                    <AvatarImage src={getAvatarUrl() || undefined} alt={getDisplayName()} />
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {getDisplayName().charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium leading-none">{getDisplayName()}</p>
                      {isPremium() && <Crown className="h-3 w-3 text-yellow-500" />}
                      {isAdmin() && <Badge variant="secondary" className="text-xs">Admin</Badge>}
                    </div>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleProfileClick}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                {/* <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem> */}
                <DropdownMenuItem onClick={handleSignOut} className="text-red-600 focus:text-red-600">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => window.location.href = "/auth/sign-in"}
            >
              Sign In
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
