"use client"

import { useState, useEffect } from "react"
import {
  User,
  Mail,
  Calendar,
  Crown,
  Edit,
  X,
  Check,
  Clock,
  CheckCircle,
  Camera,
  Loader2,
  Shield,
  Settings
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { useUserStore } from "@/store/userStore"
import { useIsMobile } from "@/hooks/use-mobile"
import { toast } from "sonner"
import { motion, AnimatePresence } from "framer-motion"
// Import Better Auth UI Components
import {
  ChangePasswordCard,
  DeleteAccountCard,
  ProvidersCard,
  SessionsCard
} from "@daveyplate/better-auth-ui"

export function ProfileContent() {
  const {
    user,
    stats,
    getDisplayName,
    getAvatarUrl,
    isPremium,
    isAdmin,
    updateUser,
    isLoading: userLoading
  } = useUserStore()

  const isMobile = useIsMobile()

  const [isEditing, setIsEditing] = useState(false)
  const [editedName, setEditedName] = useState(user?.name || "")
  const [isLoading, setIsLoading] = useState(false)

  // Update edited name when user changes
  useEffect(() => {
    if (user?.name) {
      setEditedName(user.name)
    }
  }, [user?.name])

  const handleSaveProfile = async () => {
    if (!user || editedName.trim() === "") {
      toast.error("Name cannot be empty")
      return
    }

    if (editedName.trim() === user.name) {
      setIsEditing(false)
      return
    }

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 800)) // Simulate API call
      updateUser({ name: editedName.trim() })
      setIsEditing(false)
      toast.success("Profile updated successfully", {
        description: "Your changes have been saved."
      })
    } catch (error) {
      toast.error("Failed to update profile", {
        description: "Please try again later."
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEditedName(user?.name || "")
    setIsEditing(false)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatRelativeDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Today"
    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`
    return `${Math.floor(diffDays / 365)} years ago`
  }



  if (userLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-3">
          <Loader2 className="h-8 w-8 text-primary mx-auto animate-spin" />
          <h3 className="text-lg font-medium">Loading Profile</h3>
          <p className="text-muted-foreground">Please wait while we fetch your information</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-center min-h-[400px]"
      >
        <div className="text-center space-y-4">
          <div className="h-20 w-20 rounded-full bg-muted/30 mx-auto flex items-center justify-center">
            <User className="h-10 w-10 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-medium">Not Signed In</h3>
            <p className="text-muted-foreground max-w-sm">
              Please sign in to view and manage your profile settings
            </p>
          </div>
          <Button onClick={() => window.location.href = "/auth/sign-in"} className="mt-4">
            Sign In
          </Button>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`space-y-4 sm:space-y-6 ${isMobile ? 'profile-content-mobile' : ''}`}
    >
      {/* Profile Header */}
      <Card className="border-border/40 shadow-sm">
        <CardHeader className="pb-3 sm:pb-4">
          <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
            <User className="h-4 w-4" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6">
          {/* Mobile-first responsive layout */}
          <div className="flex flex-col sm:flex-row sm:items-start gap-4 sm:gap-6">
            {/* Avatar Section - Centered on mobile, left-aligned on desktop */}
            <div className="relative group flex justify-center sm:justify-start">
              <div className="relative">
                <Avatar className="h-20 w-20 sm:h-16 sm:w-16 ring-2 ring-border">
                  <AvatarImage src={getAvatarUrl() || undefined} alt={getDisplayName()} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/20 text-primary text-xl sm:text-lg font-semibold">
                    {getDisplayName().charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <Button
                  size="sm"
                  variant="secondary"
                  className={`absolute -bottom-1 -right-1 h-8 w-8 sm:h-6 sm:w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg touch-manipulation ${isMobile ? 'profile-avatar-button' : ''}`}
                  style={{ minHeight: '32px', minWidth: '32px' }}
                >
                  <Camera className="h-4 w-4 sm:h-3 sm:w-3" />
                </Button>
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1 space-y-3 sm:space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 sm:gap-4">
                <div className="space-y-2 flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                    <AnimatePresence mode="wait">
                      {isEditing ? (
                        <motion.div
                          key="editing"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="flex items-center gap-2 flex-1"
                        >
                          <Input
                            value={editedName}
                            onChange={(e) => setEditedName(e.target.value)}
                            className="text-lg font-semibold border-primary/20 focus:border-primary h-10 sm:h-8"
                            placeholder="Enter your name"
                            onKeyDown={(e) => {
                              if (e.key === "Enter") handleSaveProfile()
                              if (e.key === "Escape") handleCancelEdit()
                            }}
                            autoFocus
                          />
                          <Button
                            size="sm"
                            onClick={handleSaveProfile}
                            disabled={isLoading}
                            className={`h-10 w-10 sm:h-8 sm:w-8 p-0 touch-manipulation ${isMobile ? 'profile-edit-button' : ''}`}
                            style={{ minHeight: '40px', minWidth: '40px' }}
                          >
                            {isLoading ? <Loader2 className="h-4 w-4 sm:h-3 sm:w-3 animate-spin" /> : <Check className="h-4 w-4 sm:h-3 sm:w-3" />}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancelEdit}
                            disabled={isLoading}
                            className={`h-10 w-10 sm:h-8 sm:w-8 p-0 touch-manipulation ${isMobile ? 'profile-edit-button' : ''}`}
                            style={{ minHeight: '40px', minWidth: '40px' }}
                          >
                            <X className="h-4 w-4 sm:h-3 sm:w-3" />
                          </Button>
                        </motion.div>
                      ) : (
                        <motion.div
                          key="display"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="flex items-center gap-2 group flex-1"
                        >
                          <h1 className="text-xl sm:text-lg font-semibold text-center sm:text-left">{getDisplayName()}</h1>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => setIsEditing(true)}
                            className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 sm:h-6 sm:w-6 p-0 touch-manipulation"
                            style={{ minHeight: '32px', minWidth: '32px' }}
                          >
                            <Edit className="h-4 w-4 sm:h-3 sm:w-3" />
                          </Button>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2 justify-center sm:justify-start">
                      <Mail className="h-3 w-3" />
                      <span className={`break-all ${isMobile ? 'profile-text-selectable' : ''}`}>{user.email}</span>
                    </div>
                    {user.emailVerified && (
                      <Badge variant="secondary" className={`text-xs h-6 sm:h-5 w-fit mx-auto sm:mx-0 ${isMobile ? 'profile-badge' : ''}`}>
                        <CheckCircle className="h-2 w-2 mr-1" />
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Badges - Centered on mobile, right-aligned on desktop */}
                <div className="flex items-center justify-center sm:justify-end gap-2 sm:gap-1 sm:ml-2">
                  {isPremium() && (
                    <Badge className={`bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0 shadow-sm text-xs h-6 sm:h-5 ${isMobile ? 'profile-badge' : ''}`}>
                      <Crown className="h-3 w-3 sm:h-2 sm:w-2 mr-1" />
                      Premium
                    </Badge>
                  )}
                  {isAdmin() && (
                    <Badge variant="outline" className={`border-blue-200 text-blue-700 bg-blue-50 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-300 text-xs h-6 sm:h-5 ${isMobile ? 'profile-badge' : ''}`}>
                      <Shield className="h-3 w-3 sm:h-2 sm:w-2 mr-1" />
                      Admin
                    </Badge>
                  )}
                </div>
              </div>

              {/* Account Details - Stack on mobile, grid on desktop */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 pt-3 sm:pt-2 border-t border-border/50">
                <div className="space-y-1 text-center sm:text-left">
                  <Label className="text-xs font-medium text-muted-foreground">Join Date</Label>
                  <div className="flex items-center gap-2 justify-center sm:justify-start">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm sm:text-xs">{formatDate(stats.joinedDate)}</span>
                  </div>
                </div>

                <div className="space-y-1 text-center sm:text-left">
                  <Label className="text-xs font-medium text-muted-foreground">Last Active</Label>
                  <div className="flex items-center gap-2 justify-center sm:justify-start">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm sm:text-xs">{formatRelativeDate(stats.lastActiveDate)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings Section */}
      <Card className="border-border/40 shadow-sm">
        <CardHeader className="pb-3 sm:pb-4">
          <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
            <Settings className="h-4 w-4" />
            Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6">
          {/* Security */}
          <div className="space-y-3 sm:space-y-4">
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-foreground">Security</h3>
              <p className="text-xs text-muted-foreground">Manage your password and authentication settings</p>
            </div>
            {/* Mobile-first responsive grid - stack on mobile, side-by-side on larger screens */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
              <div className={`w-full ${isMobile ? 'profile-card' : ''}`}>
                <ChangePasswordCard />
              </div>
              <div className={`w-full ${isMobile ? 'profile-card' : ''}`}>
                <ProvidersCard />
              </div>
            </div>
          </div>

          <Separator />

          {/* Account Management */}
          <div className="space-y-3 sm:space-y-4">
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-foreground">Account Management</h3>
              <p className="text-xs text-muted-foreground">Manage active sessions and account deletion</p>
            </div>
            {/* Mobile-first responsive grid - stack on mobile, side-by-side on larger screens */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
              <div className={`w-full ${isMobile ? 'profile-card' : ''}`}>
                <SessionsCard />
              </div>
              <div className={`w-full ${isMobile ? 'profile-card' : ''}`}>
                <DeleteAccountCard />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}