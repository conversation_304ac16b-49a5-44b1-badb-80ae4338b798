---
description:
globs:
alwaysApply: false
---
# Authentication System

## Authentication Library
The app uses Polar's Better Auth and Better Auth UI libraries for authentication:
- `@polar-sh/better-auth` - Core authentication functionality
- `@daveyplate/better-auth-ui` - UI components for auth flows

## Auth Routes
Authentication routes are located in [src/app/auth](mdc:src/app/auth) directory.

## Auth Integration
- [src/components/providers](mdc:src/components/providers) includes authentication providers
- [src/lib/auth.ts](mdc:src/lib/auth.ts) contains auth utility functions
- Protected routes require authentication before access

## User Handling
- User accounts link to the `User` model in the Prisma schema
- User profiles include subscription status for feature access
- Authentication state is available via hooks in React components

## Session Management
- Sessions are handled securely
- Middleware in [src/middleware.ts](mdc:src/middleware.ts) protects routes
- Auth redirects for unauthenticated users
