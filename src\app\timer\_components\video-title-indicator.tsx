'use client';

import { useState, useEffect, useCallback, memo, useMemo } from 'react';
import { usePomodoroStore, type Video } from '@/lib/pomodoro-store';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from '@/components/ui/sheet';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { isMobileDevice } from '@/lib/device-utils';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { Check, Video as VideoIcon, X } from 'lucide-react';

// VideoGenre enum from Prisma schema
const VIDEO_GENRES = [
  'MEDITATION',
  'AMBIENT',
  'NATURE',
  'FOREST',
  'OCEAN',
  'RAIN',
  'SNOW',
  'WATERFALL',
  'STREAM',
  'MOUNTAIN',
  'GARDEN',
  'SUNRISE_SUNSET',
  'NIGHT_SKY',
  'COFFEE',
  'HOUSE',
  'ANIMALS',
  'TRAVEL',
  'ART',
  'MINIMAL',
  'ABSTRACT',
  'STUDY',
  'LOFI'
] as const;

// Helper function to format genre names for display
const formatGenreName = (genre: string): string => {
  return genre
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

interface VideoTitleIndicatorProps {
  currentContent: any;
  canNavigate: boolean;
  contentIndicatorReady: boolean;
  isMobile: boolean;
  showControls: boolean;
  onVideoSelect?: (video: Video) => void;
}

export const VideoTitleIndicator = memo(function VideoTitleIndicator({
  currentContent,
  canNavigate,
  contentIndicatorReady,
  isMobile,
  showControls,
  onVideoSelect
}: VideoTitleIndicatorProps) {
  const [isVideoSelectionOpen, setIsVideoSelectionOpen] = useState(false);
  const [isDeviceMobile, setIsDeviceMobile] = useState(false);
  const [isVideoSwitching, setIsVideoSwitching] = useState(false);
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);

  const videos = usePomodoroStore((state) => state.videos);
  const selectedVideo = usePomodoroStore((state) => state.selectedVideo);
  const selectVideo = usePomodoroStore((state) => state.selectVideo);
  const setVideoSwitching = usePomodoroStore((state) => state.setVideoSwitching);

  // Check if device is mobile
  useEffect(() => {
    setIsDeviceMobile(isMobileDevice());
  }, []);

  // Filter videos based on selected genres (AND logic - videos must have ALL selected genres)
  const filteredVideos = useMemo(() => {
    if (selectedGenres.length === 0) {
      return videos;
    }

    return videos.filter(video => {
      if (!video.videoGenre || video.videoGenre.length === 0) {
        return false;
      }

      // Check if the video contains ALL selected genres (AND logic)
      return selectedGenres.every(selectedGenre =>
        video.videoGenre!.includes(selectedGenre)
      );
    });
  }, [videos, selectedGenres]);

  // Handle genre filter toggle
  const handleGenreToggle = useCallback((genre: string) => {
    setSelectedGenres(prev => {
      if (prev.includes(genre)) {
        return prev.filter(g => g !== genre);
      } else {
        return [...prev, genre];
      }
    });
  }, []);

  // Clear all genre filters
  const handleClearFilters = useCallback(() => {
    setSelectedGenres([]);
  }, []);

  // Handle video selection with loading state
  const handleVideoSelect = useCallback(async (video: Video) => {
    if (video.id === selectedVideo?.id || isVideoSwitching) return;
    
    setIsVideoSwitching(true);
    setIsVideoSelectionOpen(false);
    
    // Show loading indicator
    setVideoSwitching(true);
    
    try {
      // Preload the video
      const preloadVideo = document.createElement('video');
      preloadVideo.src = video.src;
      preloadVideo.autoplay = false;
      preloadVideo.loop = true;
      preloadVideo.muted = true;
      preloadVideo.playsInline = true;
      preloadVideo.preload = 'auto';

      // Preload thumbnail
      const preloadThumbnail = document.createElement('img');
      preloadThumbnail.src = video.thumbnail;

      let thumbnailLoaded = false;
      let videoLoaded = false;

      const checkIfReady = () => {
        if (thumbnailLoaded && videoLoaded) {
          selectVideo(video);
          setTimeout(() => {
            setVideoSwitching(false);
            setIsVideoSwitching(false);
          }, 500);
          
          if (onVideoSelect) {
            onVideoSelect(video);
          }
        }
      };

      preloadThumbnail.onload = () => {
        thumbnailLoaded = true;
        checkIfReady();
      };

      preloadThumbnail.onerror = () => {
        thumbnailLoaded = true;
        checkIfReady();
      };

      preloadVideo.oncanplay = () => {
        if (preloadVideo.readyState >= 3) {
          videoLoaded = true;
          checkIfReady();
        }
      };

      preloadVideo.onerror = () => {
        setVideoSwitching(false);
        setIsVideoSwitching(false);
        selectVideo(video);
        
        if (onVideoSelect) {
          onVideoSelect(video);
        }
      };
    } catch (error) {
      console.error('Error preloading video:', error);
      setVideoSwitching(false);
      setIsVideoSwitching(false);
      selectVideo(video);
      
      if (onVideoSelect) {
        onVideoSelect(video);
      }
    }
  }, [selectedVideo, isVideoSwitching, selectVideo, setVideoSwitching, onVideoSelect]);

  // Handle click to open video selection
  const handleTitleClick = useCallback(() => {
    if (videos.length > 1) {
      setIsVideoSelectionOpen(true);
    }
  }, [videos.length]);

  // Video selection content component
  const VideoSelectionContent = useCallback(() => (
    <div className="flex flex-col h-full overflow-hidden">
      <div className="text-sm text-muted-foreground mb-4 flex-shrink-0">
        Choose a background scenery for your focus session
      </div>

      {/* Genre Filter Section - Sticky Header */}
      <div className="flex-shrink-0 bg-background/95 backdrop-blur-sm border-b border-border/40 pb-4 mb-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-sm font-medium">Filter by Genre</h3>
              {selectedGenres.length > 1 && (
                <p className="text-xs text-muted-foreground">
                  Videos must match <strong>all</strong> selected genres
                </p>
              )}
            </div>
            {selectedGenres.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="h-6 px-2 text-xs hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-950/20 dark:hover:text-red-400"
              >
                <X className="h-3 w-3 mr-1" />
                Clear All
              </Button>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            {VIDEO_GENRES.map((genre) => (
              <Badge
                key={genre}
                variant={selectedGenres.includes(genre) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all duration-200 hover:scale-105",
                  selectedGenres.includes(genre)
                    ? "bg-red-500 hover:bg-red-600 text-white border-red-500"
                    : "hover:bg-red-50 hover:text-red-700 hover:border-red-200 dark:hover:bg-red-950/20 dark:hover:text-red-400 dark:hover:border-red-800"
                )}
                onClick={() => handleGenreToggle(genre)}
              >
                {formatGenreName(genre)}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Video Grid Section - Scrollable */}
      <div className="flex-1 min-h-0 overflow-y-auto scrollbar-thin">
        <div className="flex items-center justify-between mb-3 flex-shrink-0">
          <p className="text-xs text-muted-foreground">
            {filteredVideos.length} video{filteredVideos.length !== 1 ? 's' : ''} available
            {selectedGenres.length > 0 && (
              <span>
                {selectedGenres.length === 1
                  ? ` (matching "${formatGenreName(selectedGenres[0])}")`
                  : ` (matching all ${selectedGenres.length} selected genres)`
                }
              </span>
            )}
          </p>
        </div>

        <div className="h-full scrollbar-thin">
          <div className="pr-4 pb-8">
            <AnimatePresence mode="wait">
              {filteredVideos.length > 0 ? (
                <motion.div
                  key="video-grid"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
                >
                  {filteredVideos.map((video) => (
                    <VideoCard
                      key={video.id}
                      video={video}
                      isSelected={selectedVideo?.id === video.id}
                      onSelect={handleVideoSelect}
                      isLoading={isVideoSwitching && selectedVideo?.id !== video.id}
                    />
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="no-videos"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="flex flex-col items-center justify-center py-16 text-center"
                >
                  <VideoIcon className="h-16 w-16 text-muted-foreground/50 mb-6" />
                  <p className="text-sm text-muted-foreground mb-2">
                    No videos found matching all selected genres
                  </p>
                  <p className="text-xs text-muted-foreground/70 mb-4 max-w-md">
                    Try selecting fewer genres or different combinations to find videos
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearFilters}
                    className="hover:bg-red-50 hover:text-red-700 hover:border-red-200 dark:hover:bg-red-950/20 dark:hover:text-red-400 dark:hover:border-red-800"
                  >
                    Clear All Filters
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  ), [filteredVideos, selectedVideo, handleVideoSelect, isVideoSwitching, selectedGenres, handleGenreToggle, handleClearFilters]);

  // Show loading state when switching videos
  if (isVideoSwitching) {
    return (
      <div
        className={`fixed z-50 transition-opacity duration-300 ${
          isMobile
            ? 'bottom-3 left-3'
            : 'top-3 sm:top-4 left-14 sm:left-16'
        } ${showControls ? 'opacity-100' : 'opacity-0'}`}
      >
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="flex items-center gap-2 bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 rounded-lg px-2 sm:px-3 py-1.5"
        >
          <span className="text-xs sm:text-sm whitespace-nowrap">
            Loading new scenery...
          </span>
        </motion.div>
      </div>
    );
  }

  return (
    <>
      <div
        className={`fixed z-50 transition-opacity duration-300 ${
          isMobile
            ? 'bottom-3 left-3'
            : 'top-3 sm:top-4 left-14 sm:left-16'
        } ${showControls ? 'opacity-100' : 'opacity-0'}`}
      >
        {currentContent && canNavigate ? (
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className={cn(
                    "flex items-center gap-2 bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 rounded-lg px-2 sm:px-3 py-1.5 transition-all duration-200",
                    contentIndicatorReady ? 'opacity-100' : 'opacity-30',
                    videos.length > 1 ? 'cursor-pointer hover:scale-105' : 'cursor-default'
                  )}
                  onClick={handleTitleClick}
                  role={videos.length > 1 ? "button" : undefined}
                  aria-label={videos.length > 1 ? "Change video background" : undefined}
                  tabIndex={videos.length > 1 ? 0 : -1}
                  onKeyDown={(e) => {
                    if (videos.length > 1 && (e.key === 'Enter' || e.key === ' ')) {
                      e.preventDefault();
                      handleTitleClick();
                    }
                  }}
                >
                  <span
                    className={`text-xs sm:text-sm whitespace-nowrap text-ellipsis overflow-hidden ${isMobile ? 'max-w-[120px]' : 'max-w-[200px]'}`}
                    title={currentContent.title}
                  >
                    {currentContent.title}
                  </span>
                </motion.div>
              </TooltipTrigger>
              {videos.length > 1 && (
                <TooltipContent side="bottom" className="z-[60]">
                  <p className="text-xs">Change video background</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        ) : (
          <div
            className={`flex items-center justify-center bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 rounded-lg px-2 sm:px-3 py-1.5 ${contentIndicatorReady ? 'opacity-100' : 'opacity-30'}`}
            style={{ width: isMobile ? '120px' : '180px' }}
          >
            <div className="flex items-center justify-center h-5 flex-grow">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse" />
                <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse delay-75" />
                <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse delay-150" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Video Selection Dialog/Sheet */}
      {isDeviceMobile ? (
        <Sheet open={isVideoSelectionOpen} onOpenChange={setIsVideoSelectionOpen}>
          <SheetContent
            side="bottom"
            className="p-0 min-h-[80vh] max-h-[95vh] h-[95vh] [&>button]:hidden flex flex-col"
          >
            <SheetHeader className="sr-only">
              <SheetTitle>Select Background Video</SheetTitle>
              <SheetDescription>Choose a background scenery for your focus session</SheetDescription>
            </SheetHeader>

            <div className="flex h-full w-full flex-col flex-1 p-4 overflow-y-auto scrollbar-thin">
              <VideoSelectionContent />
            </div>
          </SheetContent>
        </Sheet>
      ) : (
        <Dialog open={isVideoSelectionOpen} onOpenChange={setIsVideoSelectionOpen}>
          <DialogContent className="max-w-[calc(100%-2rem)] sm:max-w-[70vw] max-h-[90vh] flex flex-col overflow-hidden">
            <DialogHeader>
              <DialogTitle>Select Background Video</DialogTitle>
            </DialogHeader>

            <div className="flex-1 min-h-0 overflow-y-auto scrollbar-thin">
              <VideoSelectionContent />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
});

// Video card component for the selection grid
const VideoCard = memo(function VideoCard({
  video,
  isSelected,
  onSelect,
  isLoading
}: {
  video: Video;
  isSelected: boolean;
  onSelect: (video: Video) => void;
  isLoading?: boolean;
}) {
  const handleSelect = useCallback(() => {
    if (!isLoading) {
      onSelect(video);
    }
  }, [video, onSelect, isLoading]);

  return (
    <div
      className={cn(
        "relative group cursor-pointer rounded-xl overflow-hidden border-2 transition-all duration-200 shadow-sm hover:shadow-lg",
        isSelected
          ? "border-red-500 ring-2 ring-red-500/20 shadow-red-500/20"
          : "border-transparent hover:border-white/30 dark:hover:border-white/20",
        isLoading && "opacity-50 cursor-not-allowed"
      )}
      onClick={handleSelect}
    >
      <AspectRatio ratio={16 / 9} className="bg-muted">
        {video.thumbnail ? (
          <Image
            src={video.thumbnail}
            alt={video.title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={false}
          />
        ) : (
          <div className="absolute inset-0 bg-muted flex items-center justify-center">
            <VideoIcon className="h-16 w-16 text-muted-foreground/50" />
          </div>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-colors duration-300" />

        {/* Selection indicator */}
        {isSelected && (
          <div className="absolute top-3 right-3 bg-red-500 text-white rounded-full p-2 shadow-lg">
            <Check className="h-4 w-4" />
          </div>
        )}

        {/* Loading indicator */}
        {isLoading && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
              <div className="w-2 h-2 bg-white rounded-full animate-pulse delay-75" />
              <div className="w-2 h-2 bg-white rounded-full animate-pulse delay-150" />
            </div>
          </div>
        )}

        {/* Title overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4">
          <p className="text-white text-sm font-medium leading-tight">
            {video.title}
          </p>
        </div>
      </AspectRatio>
    </div>
  );
});
