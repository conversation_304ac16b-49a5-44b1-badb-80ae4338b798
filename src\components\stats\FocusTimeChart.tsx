"use client"

import { useRef, useMemo } from "react"
import { useTheme } from "next-themes"
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON>s, LabelList, ReferenceLine } from "recharts"
import { formatMinutesReadable } from "@/lib/utils"
import { TrendingUp, TrendingDown, Minus } from "lucide-react"

interface FocusTimeChartProps {
  data: Array<{
    date: string
    minutes: number
  }>
}

interface TooltipProps {
  active?: boolean
  payload?: Array<{ value: number }>
  label?: string
}

export function FocusTimeChart({ data }: FocusTimeChartProps) {
  const { theme } = useTheme()
  const tooltipRef = useRef<HTMLDivElement>(null)

  // Calculate summary statistics
  const stats = useMemo(() => {
    if (!data || data.length === 0) {
      return { average: 0, peak: 0, total: 0, trend: 'neutral' as const, activeDays: 0 }
    }

    const values = data.map(d => d.minutes)
    const total = values.reduce((sum, val) => sum + val, 0)
    const average = Math.round(total / data.length)
    const peak = Math.max(...values)
    const activeDays = values.filter(val => val > 0).length

    // Calculate trend (compare first half vs second half)
    const midPoint = Math.floor(data.length / 2)
    const firstHalf = data.slice(0, midPoint)
    const secondHalf = data.slice(midPoint)

    const firstHalfAvg = firstHalf.reduce((sum, d) => sum + d.minutes, 0) / firstHalf.length
    const secondHalfAvg = secondHalf.reduce((sum, d) => sum + d.minutes, 0) / secondHalf.length

    const trendDiff = secondHalfAvg - firstHalfAvg
    const trend = Math.abs(trendDiff) < 5 ? 'neutral' : trendDiff > 0 ? 'up' : 'down'

    return { average, peak, total, trend, activeDays }
  }, [data])

  // Format date for x-axis
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.getDate().toString()
  }

  // Custom label formatter for data points - only show on significant values
  const renderCustomLabel = (props: any) => {
    const { x, y, value } = props
    if (!value || value === 0 || value < stats.average * 0.8) return null

    return (
      <text
        x={x}
        y={y - 12}
        fill="hsl(var(--chart-3))"
        textAnchor="middle"
        fontSize={10}
        fontWeight={600}
        className="drop-shadow-sm"
      >
        {formatMinutesReadable(value)}
      </text>
    )
  }

  // Enhanced custom tooltip with better formatting and insights
  const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
    if (active && payload && payload.length) {
      const date = new Date(label || "")
      const formattedDate = date.toLocaleDateString("en-US", {
        weekday: "short",
        month: "short",
        day: "numeric"
      })
      const minutes = payload[0].value || 0
      const formattedTime = formatMinutesReadable(minutes)

      // Calculate percentage of average
      const percentOfAverage = stats.average > 0 ? Math.round((minutes / stats.average) * 100) : 0

      // Determine performance indicator
      let indicator = { text: "No activity", color: "text-muted-foreground" }
      if (minutes > 0) {
        if (minutes >= stats.average * 1.2) {
          indicator = { text: "Above average", color: "text-chart-2" }
        } else if (minutes >= stats.average * 0.8) {
          indicator = { text: "Average day", color: "text-chart-3" }
        } else {
          indicator = { text: "Below average", color: "text-chart-5" }
        }
      }

      return (
        <div
          ref={tooltipRef}
          className="rounded-lg border border-border bg-gradient-to-br from-card/95 to-card/90 backdrop-blur-sm p-4 shadow-xl"
          role="tooltip"
          aria-label={`Focus time for ${formattedDate}: ${formattedTime}`}
        >
          <p className="mb-2 font-semibold text-foreground">{formattedDate}</p>
          <div className="space-y-1">
            <p className="text-chart-3 font-bold text-lg">
              {formattedTime}
            </p>
            <p className={`text-xs font-medium ${indicator.color}`}>
              {indicator.text}
            </p>
            {minutes > 0 && stats.average > 0 && (
              <p className="text-xs text-muted-foreground">
                {percentOfAverage}% of your average
              </p>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-4">
      {/* Summary Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <div className="text-center p-2 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200">
          <p className="text-xs text-muted-foreground font-medium">Average</p>
          <p className="text-sm font-bold text-chart-3" aria-label={`Average focus time: ${formatMinutesReadable(stats.average)}`}>
            {formatMinutesReadable(stats.average)}
          </p>
        </div>
        <div className="text-center p-2 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200">
          <p className="text-xs text-muted-foreground font-medium">Peak Day</p>
          <p className="text-sm font-bold text-chart-2" aria-label={`Peak focus time: ${formatMinutesReadable(stats.peak)}`}>
            {formatMinutesReadable(stats.peak)}
          </p>
        </div>
        <div className="text-center p-2 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200">
          <p className="text-xs text-muted-foreground font-medium">Active Days</p>
          <p className="text-sm font-bold text-chart-4" aria-label={`${stats.activeDays} active days`}>
            {stats.activeDays}
          </p>
        </div>
        <div className="text-center p-2 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200">
          <p className="text-xs text-muted-foreground font-medium flex items-center justify-center gap-1">
            Trend
            {stats.trend === 'up' && <TrendingUp className="h-3 w-3 text-chart-2" />}
            {stats.trend === 'down' && <TrendingDown className="h-3 w-3 text-chart-5" />}
            {stats.trend === 'neutral' && <Minus className="h-3 w-3 text-muted-foreground" />}
          </p>
          <p className={`text-sm font-bold ${
            stats.trend === 'up' ? 'text-chart-2' :
            stats.trend === 'down' ? 'text-chart-5' :
            'text-muted-foreground'
          }`}>
            {stats.trend === 'up' ? 'Improving' : stats.trend === 'down' ? 'Declining' : 'Stable'}
          </p>
        </div>
      </div>

      {/* Chart */}
      <div className="h-[280px] w-full" role="img" aria-label="Focus time trend chart showing daily focus minutes over time">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 30, right: 15, left: 10, bottom: 10 }}
            accessibilityLayer
          >
            <defs>
              <linearGradient id="line-gradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#f97316" stopOpacity={0.9} />
                <stop offset="50%" stopColor="#ef4444" stopOpacity={0.7} />
                <stop offset="100%" stopColor="#e11d48" stopOpacity={0.4} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="date"
              tickFormatter={formatDate}
              stroke="hsl(var(--muted-foreground))"
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 11, fill: "hsl(var(--muted-foreground))" }}
              dy={8}
            />
            <YAxis
              stroke="hsl(var(--muted-foreground))"
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 11, fill: "hsl(var(--muted-foreground))" }}
              width={35}
              tickFormatter={(value) => value > 0 ? formatMinutesReadable(value) : '0'}
            />
            {/* Average reference line */}
            {stats.average > 0 && (
              <ReferenceLine
                y={stats.average}
                stroke="hsl(var(--chart-3))"
                strokeDasharray="3 3"
                strokeOpacity={0.6}
                label={{
                  value: `Avg: ${formatMinutesReadable(stats.average)}`,
                  position: "right",
                  fontSize: 10,
                  fill: "hsl(var(--chart-3))"
                }}
              />
            )}
            <Tooltip content={<CustomTooltip />} />
            <Line
              type="natural"
              dataKey="minutes"
              stroke="url(#line-gradient)"
              strokeWidth={3}
              connectNulls={true}
              isAnimationActive={true}
              animationDuration={1500}
              animationEasing="ease-out"
              dot={{
                r: 4,
                fill: "#f97316",
                stroke: "hsl(var(--background))",
                strokeWidth: 2
              }}
              activeDot={{
                r: 7,
                fill: "#e11d48",
                stroke: "hsl(var(--background))",
                strokeWidth: 3,
                className: "drop-shadow-lg"
              }}
            >
              <LabelList content={renderCustomLabel} />
            </Line>
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
