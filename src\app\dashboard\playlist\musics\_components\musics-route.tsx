"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import {
  Search,
  Plus,
  Music2,
  Star,
  Filter,
  X,
  Play,
  Pause,
  Clock,
  Heart,
  Headphones
} from "lucide-react"
import { useGetMusics } from "@schemas/Music/music-query"
import { useAddMusicToMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { AddToPlaylistDialog } from "../../_components/add-to-playlist-dialog"

interface Music {
  id: string
  title: string
  src: string | null
  genres: string[]
  isPublic: boolean
  rating?: number | null
  duration?: number | null
  note?: string | null
}

export function MusicsRoute() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [selectedMusicId, setSelectedMusicId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  // Mobile detection for responsive behavior
  const isMobile = useIsMobile()

  const { data: musics, isLoading } = useGetMusics({ isPublic: true })
  const addMusicToPlaylist = useAddMusicToMusicPlaylistUser()

  // Global audio player state
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying
  } = useAudioStore()

  // Get unique genres for filtering
  const availableGenres = useMemo(() => {
    if (!musics) return []
    const genreSet = new Set<string>()
    musics.forEach(music => {
      music.genres?.forEach(genre => genreSet.add(genre))
    })
    return Array.from(genreSet).sort()
  }, [musics])

  // Filter musics based on search and genre filters
  const filteredMusics = useMemo(() => {
    if (!musics) return []

    return musics.filter(music => {
      const matchesSearch = music.title.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesGenre = selectedGenres.length === 0 ||
        music.genres?.some(genre => selectedGenres.includes(genre))

      return matchesSearch && matchesGenre
    })
  }, [musics, searchQuery, selectedGenres])

  // Enhanced stats with more insights
  const stats = useMemo(() => {
    if (!musics) return { total: 0, genres: 0, totalDuration: 0, avgRating: 0 }

    const total = musics.length
    const uniqueGenres = new Set<string>()
    let totalRating = 0
    let ratedCount = 0
    let totalDurationSeconds = 0

    musics.forEach(music => {
      music.genres?.forEach(genre => uniqueGenres.add(genre))
      if (music.rating) {
        totalRating += music.rating
        ratedCount++
      }
      if (music.duration) {
        totalDurationSeconds += music.duration
      }
    })

    return {
      total,
      genres: uniqueGenres.size,
      totalDuration: Math.floor(totalDurationSeconds / 60), // Convert to minutes
      avgRating: ratedCount > 0 ? totalRating / ratedCount : 0
    }
  }, [musics])

  const handleGenreToggle = (genre: string) => {
    setSelectedGenres(prev => 
      prev.includes(genre) 
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    )
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedGenres([])
  }

  const handleAddToPlaylist = (musicId: string) => {
    setSelectedMusicId(musicId)
    setIsAddDialogOpen(true)
  }

  const handleAddMusicToPlaylist = async (playlistId: string) => {
    if (!selectedMusicId) return

    await addMusicToPlaylist.mutateAsync({
      musicPlaylistUserId: playlistId,
      musicIds: [selectedMusicId]
    })
  }

  const handlePlayMusic = (music: Music) => {
    const track = {
      id: music.id,
      title: music.title,
      src: music.src || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
      type: 'music' as const,
      genres: music.genres
    }

    if (globalPlayer.currentTrack?.id === music.id) {
      // If already playing this track, toggle play/pause
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    } else {
      // Play new track - set track first, then ensure it starts playing
      setGlobalPlayerTrack(track)
      setGlobalPlayerPlaying(true)
    }
  }

  const selectedMusic = selectedMusicId ? musics?.find(m => m.id === selectedMusicId) : null

  // Mobile-Optimized Loading State
  if (isLoading) {
    return (
      <div className="space-y-4 sm:space-y-6">
        {/* Mobile-Optimized Header skeleton */}
        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3">
              <Skeleton className="h-8 w-8 sm:h-10 sm:w-10 rounded-xl" />
              <div className="space-y-1 sm:space-y-2">
                <Skeleton className="h-5 w-32 sm:h-6 sm:w-48" />
                <Skeleton className="h-3 w-48 sm:h-4 sm:w-64" />
              </div>
            </div>
          </div>
        </div>

        {/* Mobile-Optimized Search and filters skeleton */}
        <Card>
          <CardContent className="p-3 sm:p-4 space-y-3 sm:space-y-4">
            <Skeleton className="h-9 sm:h-10 w-full" />
            <div className="flex flex-wrap gap-1.5 sm:gap-2">
              {Array.from({ length: isMobile ? 3 : 5 }).map((_, i) => (
                <Skeleton key={i} className="h-5 w-12 sm:h-6 sm:w-16" />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Mobile-Optimized Grid skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          {Array.from({ length: isMobile ? 6 : 9 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-2.5 sm:p-3">
                <div className="flex items-center gap-2.5 sm:gap-3">
                  <Skeleton className="h-9 w-9 sm:h-10 sm:w-10 rounded-lg shrink-0" />
                  <div className="flex-1 space-y-1.5 sm:space-y-2">
                    <Skeleton className="h-3.5 w-3/4 sm:h-4" />
                    <div className="flex gap-1">
                      <Skeleton className="h-2.5 w-10 sm:h-3 sm:w-12" />
                      <Skeleton className="h-2.5 w-12 sm:h-3 sm:w-16" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "space-y-4 sm:space-y-6 pb-24 sm:pb-32",
      isMobile && "music-route-mobile"
    )}>
        {/* Mobile-Optimized Header */}
        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="p-1.5 sm:p-2 rounded-lg sm:rounded-xl bg-gradient-to-br from-orange-500/10 to-rose-500/10 border border-orange-200/20 dark:border-orange-700/20">
                <Music2 className="h-4 w-4 sm:h-5 sm:w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h1 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 bg-clip-text text-transparent">
                  Music Library
                </h1>
                <p className="text-xs sm:text-sm text-muted-foreground leading-tight">
                  Discover and add music tracks to enhance your focus sessions
                </p>
              </div>
            </div>
          </div>

        </div>

        {/* Mobile-Optimized Search and Filters Card */}
        <Card>
          <CardContent className="p-3 sm:p-4 space-y-3 sm:space-y-4">
            <div className="flex flex-col gap-2.5 sm:flex-row sm:gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={isMobile ? "Search tracks..." : "Search music tracks..."}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={cn(
                    "pl-10 h-9 sm:h-10 text-sm sm:text-base",
                    isMobile && "music-search-input"
                  )}
                  aria-label="Search music tracks"
                />
              </div>

              {(searchQuery || selectedGenres.length > 0) && (
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className={cn(
                    "gap-1.5 sm:gap-2 shrink-0 transition-all duration-200",
                    "hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200",
                    "dark:hover:bg-orange-950/30 dark:hover:text-orange-300 dark:hover:border-orange-800",
                    "active:scale-95 touch-action-manipulation",
                    isMobile ? "h-9 px-3 text-sm min-w-[44px]" : "h-10 px-4"
                  )}
                  aria-label="Clear all filters"
                >
                  <X className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                  {!isMobile && "Clear Filters"}
                  {isMobile && "Clear"}
                </Button>
              )}
            </div>

            {/* Mobile-Optimized Genre Filters */}
            {availableGenres.length > 0 && (
              <div className="space-y-2.5 sm:space-y-3">
                <div className="flex items-center gap-1.5 sm:gap-2">
                  <Filter className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-muted-foreground" />
                  <span className="text-xs sm:text-sm font-medium">Genres</span>
                </div>
                <div className="flex flex-wrap gap-1.5 sm:gap-2">
                  {availableGenres.map(genre => (
                    <Badge
                      key={genre}
                      variant={selectedGenres.includes(genre) ? "default" : "outline"}
                      className={cn(
                        "cursor-pointer transition-all duration-200 touch-action-manipulation",
                        "active:scale-95 select-none",
                        isMobile ? "text-xs px-2 py-1 min-h-[32px] min-w-[44px] music-filter-badge" : "text-sm hover:scale-105",
                        selectedGenres.includes(genre)
                          ? "bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 text-white border-transparent hover:from-orange-600 hover:via-red-600 hover:to-rose-700"
                          : "hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200 dark:hover:bg-orange-950/30 dark:hover:text-orange-300 dark:hover:border-orange-800"
                      )}
                      onClick={() => handleGenreToggle(genre)}
                      role="button"
                      tabIndex={0}
                      aria-pressed={selectedGenres.includes(genre)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault()
                          handleGenreToggle(genre)
                        }
                      }}
                    >
                      {genre}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Mobile-Optimized Music Display */}
        <AnimatePresence mode="wait">
        {filteredMusics.length === 0 ? (
          <motion.div
            key="empty"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center justify-center py-12 sm:py-20 text-center px-4"
          >
            {/* Mobile-Optimized Icon with Animation */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 200, damping: 15 }}
              className="relative mb-6 sm:mb-8"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-rose-400/20 rounded-full blur-xl scale-150" />
              <div className="relative p-4 sm:p-6 rounded-xl sm:rounded-2xl bg-gradient-to-br from-orange-50 via-red-50 to-rose-50 dark:from-orange-950/50 dark:via-red-950/30 dark:to-rose-950/50 border border-orange-200/50 dark:border-orange-800/50 shadow-lg">
                <motion.div
                  animate={!isMobile ? {
                    rotate: [0, -10, 10, -5, 5, 0],
                    scale: [1, 1.05, 1]
                  } : {}}
                  transition={!isMobile ? {
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3,
                    ease: "easeInOut"
                  } : {}}
                >
                  <Music2 className="h-12 w-12 sm:h-16 sm:w-16 text-orange-500 dark:text-orange-400" />
                </motion.div>
              </div>
            </motion.div>

            {/* Mobile-Optimized Content */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="space-y-4 sm:space-y-6 max-w-lg"
            >
              <div className="space-y-2 sm:space-y-3">
                <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-rose-600 bg-clip-text text-transparent">
                  {searchQuery || selectedGenres.length > 0
                    ? "No tracks match your search"
                    : "Discover amazing music"
                  }
                </h3>
                <p className="text-sm sm:text-base text-muted-foreground leading-relaxed px-2 sm:px-4">
                  {searchQuery || selectedGenres.length > 0
                    ? "Try adjusting your search terms or filters to find the perfect tracks for your focus sessions."
                    : "Explore our curated collection of focus music to enhance your productivity and concentration."
                  }
                </p>
              </div>

              {/* Mobile-Optimized Features List */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                className="grid grid-cols-3 gap-2 sm:gap-4 py-3 sm:py-4"
              >
                <div className="flex flex-col items-center gap-1.5 sm:gap-2 p-2 sm:p-3 rounded-lg bg-muted/30">
                  <div className="p-1.5 sm:p-2 rounded-full bg-orange-500/10">
                    <Music2 className="h-3 w-3 sm:h-4 sm:w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground text-center">Focus Music</span>
                </div>
                <div className="flex flex-col items-center gap-1.5 sm:gap-2 p-2 sm:p-3 rounded-lg bg-muted/30">
                  <div className="p-1.5 sm:p-2 rounded-full bg-rose-500/10">
                    <Headphones className="h-3 w-3 sm:h-4 sm:w-4 text-rose-600 dark:text-rose-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground text-center">High Quality</span>
                </div>
                <div className="flex flex-col items-center gap-1.5 sm:gap-2 p-2 sm:p-3 rounded-lg bg-muted/30">
                  <div className="p-1.5 sm:p-2 rounded-full bg-red-500/10">
                    <Heart className="h-3 w-3 sm:h-4 sm:w-4 text-red-600 dark:text-red-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground text-center">Curated Selection</span>
                </div>
              </motion.div>

              {/* Mobile-Optimized Clear Filters Button */}
              {(searchQuery || selectedGenres.length > 0) && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7, duration: 0.3 }}
                  className="flex justify-center pt-1 sm:pt-2"
                >
                  <Button
                    onClick={clearFilters}
                    size={isMobile ? "default" : "lg"}
                    className={cn(
                      "bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 group active:scale-95 touch-action-manipulation",
                      isMobile ? "px-6 h-10 min-w-[44px]" : "px-8"
                    )}
                    aria-label="Clear all search filters"
                  >
                    <X className={cn(
                      "mr-1.5 sm:mr-2 group-hover:rotate-90 transition-transform duration-200",
                      isMobile ? "h-4 w-4" : "h-5 w-5"
                    )} />
                    {isMobile ? "Clear Filters" : "Clear All Filters"}
                  </Button>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="musics"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4"
          >
            {filteredMusics.map((music, index) => {
              const isCurrentlyPlaying = globalPlayer.currentTrack?.id === music.id && globalPlayer.isPlaying
              const isCurrentTrack = globalPlayer.currentTrack?.id === music.id

              return (
                <motion.div
                  key={music.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: isMobile ? index * 0.02 : index * 0.03,
                    duration: 0.2,
                    ease: "easeOut"
                  }}
                  layout
                >
                  <Card
                    className={cn(
                      "group relative overflow-hidden transition-all duration-200 cursor-pointer h-full touch-action-manipulation",
                      "hover:shadow-lg hover:bg-accent/30 hover:border-accent-foreground/20",
                      "active:scale-[0.98] active:transition-transform active:duration-100",
                      isCurrentTrack && "bg-orange-50/50 border-orange-200 ring-1 ring-orange-200/50 dark:bg-orange-950/20 dark:border-orange-800 dark:ring-orange-800/50",
                      isMobile && "music-card-mobile"
                    )}
                  >
                    <CardContent className={cn("p-2.5 sm:p-3", isMobile && "music-control-mobile")}>
                      <div className="flex items-center gap-2.5 sm:gap-3 relative">
                        {/* Mobile-Optimized Music Icon / Play Button */}
                        <div className={cn(
                          "rounded-lg bg-muted/20 group-hover:bg-orange-50 dark:group-hover:bg-orange-950/30 flex items-center justify-center shrink-0 relative transition-colors duration-200",
                          isMobile ? "h-9 w-9" : "h-10 w-10"
                        )}>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation()
                              handlePlayMusic(music)
                            }}
                            className={cn(
                              "rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/40 absolute inset-0 transition-colors duration-150 touch-action-manipulation",
                              isMobile ? "w-9 h-9 min-w-[44px] min-h-[44px]" : "w-10 h-10"
                            )}
                            aria-label={isCurrentlyPlaying ? `Pause ${music.title}` : `Play ${music.title}`}
                          >
                            {isCurrentlyPlaying ? (
                              <Pause className={cn(
                                "text-orange-600 dark:text-orange-400",
                                isMobile ? "h-3.5 w-3.5" : "h-4 w-4"
                              )} />
                            ) : (
                              <Play className={cn(
                                "text-orange-600 dark:text-orange-400",
                                isMobile ? "h-3.5 w-3.5 ml-0.5" : "h-4 w-4 ml-0.5"
                              )} />
                            )}
                          </Button>
                        </div>

                        {/* Mobile-Optimized Music Info */}
                        <div className="flex-1 min-w-0">
                          <h3 className={cn(
                            "font-medium line-clamp-1 mb-1 sm:mb-1.5 leading-tight",
                            isMobile ? "text-sm" : "text-sm"
                          )}>{music.title}</h3>
                          <div className="flex items-center gap-1 sm:gap-1.5 flex-wrap">
                            {music.genres?.slice(0, isMobile ? 1 : 2).map(genre => (
                              <Badge
                                key={genre}
                                variant="secondary"
                                className={cn(
                                  "px-1.5 py-0.5",
                                  isMobile ? "text-xs" : "text-xs"
                                )}
                              >
                                {genre}
                              </Badge>
                            ))}
                            {music.genres && music.genres.length > (isMobile ? 1 : 2) && (
                              <Badge
                                variant="secondary"
                                className={cn(
                                  "px-1.5 py-0.5",
                                  isMobile ? "text-xs" : "text-xs"
                                )}
                              >
                                +{music.genres.length - (isMobile ? 1 : 2)}
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Mobile-Optimized Add Button */}
                        <div className={cn(
                          "transition-all duration-200",
                          isMobile
                            ? "opacity-100 static transform-none"
                            : "absolute right-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0"
                        )}>
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleAddToPlaylist(music.id)
                            }}
                            size="sm"
                            className={cn(
                              "transition-all duration-200 gap-1 shadow-lg touch-action-manipulation active:scale-95",
                              "bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white",
                              isMobile
                                ? "h-8 px-2.5 text-xs min-w-[44px] music-add-button"
                                : "h-7 px-3 text-xs hover:shadow-xl hover:scale-105"
                            )}
                            aria-label={`Add ${music.title} to playlist`}
                          >
                            <Plus className={cn(isMobile ? "h-3 w-3" : "h-3 w-3")} />
                            {!isMobile && "Add"}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        )}
        </AnimatePresence>

        {/* Add to Playlist Dialog */}
        <AddToPlaylistDialog
          isOpen={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
          itemType="music"
          itemTitle={selectedMusic?.title || ""}
          onAddToPlaylist={handleAddMusicToPlaylist}
        />
      </div>
  )
}
