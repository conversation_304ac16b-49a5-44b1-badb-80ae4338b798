import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a date string or Date object to a readable format
 */
export function formatDate(date: Date | string): string {
  return new Date(date).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}

/**
 * Format time duration in seconds to readable hour-minute format
 * Following user preferences: '3h 45m', '45m' for <1h, '0m' for zero
 */
export function formatDurationReadable(seconds: number): string {
  if (seconds <= 0) return "0m"

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours === 0) {
    return `${minutes}m`
  }

  if (minutes === 0) {
    return `${hours}h`
  }

  return `${hours}h ${minutes}m`
}

/**
 * Format time duration in minutes to readable hour-minute format
 * Following user preferences: '3h 45m', '45m' for <1h, '0m' for zero
 */
export function formatMinutesReadable(minutes: number): string {
  return formatDurationReadable(minutes * 60)
}
