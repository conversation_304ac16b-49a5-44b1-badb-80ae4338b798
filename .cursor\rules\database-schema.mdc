---
description: 
globs: 
alwaysApply: false
---
# Database Schema

## Prisma Schema
The main Prisma schema is located in [prisma/schema/schema.prisma](mdc:prisma/schema/schema.prisma). The application uses several models to manage data.

## Schema Organization
The schema is organized into domain-specific directories:
- [prisma/schema/Users](mdc:prisma/schema/Users) - User account models
- [prisma/schema/Tasks](mdc:prisma/schema/Tasks) - Task management models
- [prisma/schema/Pomodoro](mdc:prisma/schema/Pomodoro) - Timer session models
- [prisma/schema/Music](mdc:prisma/schema/Music) - Music and audio models
- [prisma/schema/Natural](mdc:prisma/schema/Natural) - Natural sound models
- [prisma/schema/Payments](mdc:prisma/schema/Payments) - Payment and subscription models

## Database Operations
Database queries use the Prisma Client to ensure type safety. The client is initialized in [prisma/index.ts](mdc:prisma/index.ts).

## Data Validation
Zod schemas are used for validating data before database operations. Auto-generated Zod types are available in [prisma/zod-auto-types](mdc:prisma/zod-auto-types).

