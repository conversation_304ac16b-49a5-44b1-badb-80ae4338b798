---
description: 
globs: 
alwaysApply: false
---
# Task Management System

## Task Components
Task management UI components are located in [src/components/task-management](mdc:src/components/task-management).

## Task Data Models
The task data structure is defined in Prisma models within [prisma/schema/Tasks](mdc:prisma/schema/Tasks).

## Core Functionality
- Task creation, editing, deletion
- Task categorization and prioritization
- Linking tasks to Pomodoro sessions
- Task completion tracking
- Task filtering and sorting

## Task State Management
Task state is managed using:
- Local Zustand stores for UI interaction
- Server-side persistence via API endpoints
- React Query for data fetching and mutations

## Drag and Drop
The task interface uses Atlaskit's pragmatic drag and drop library:
- `@atlaskit/pragmatic-drag-and-drop` - Core drag and drop functionality
- `@atlaskit/pragmatic-drag-and-drop-react-drop-indicator` - Visual indicators

## Integration with Timer
Tasks integrate with the Pomodoro timer to track work sessions per task.

