"use client"

import { useState, useRef, useCallback, useEffect } from "react"
import { useAudioStore } from "@/lib/audio-store"

export interface PlaylistTrack {
  id: string
  title: string
  src: string | null
  type: 'music' | 'nature-sound'
  genres?: string[]
  category?: string[]
  duration?: number | null
}

interface PlaylistAudioState {
  currentTrack: PlaylistTrack | null
  isPlaying: boolean
  volume: number
  isMuted: boolean
  currentTime: number
  duration: number
  playlist: PlaylistTrack[]
  currentIndex: number
}

export function usePlaylistAudio() {
  const { globalAudioControl } = useAudioStore()
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const sourceId = useRef<string>(`playlist-${Date.now()}`)
  
  const [state, setState] = useState<PlaylistAudioState>({
    currentTrack: null,
    isPlaying: false,
    volume: 80,
    isMuted: false,
    currentTime: 0,
    duration: 0,
    playlist: [],
    currentIndex: -1,
  })

  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
  }, [])

  const resume = useCallback(() => {
    if (audioRef.current && !state.isPlaying) {
      audioRef.current.play().catch(console.error)
    }
  }, [state.isPlaying])

  // Register this audio source with global control
  useEffect(() => {
    const currentSourceId = sourceId.current
    const source = {
      id: currentSourceId,
      type: 'playlist' as const,
      name: 'Playlist Player',
      isPlaying: state.isPlaying,
      volume: state.volume,
      isMuted: state.isMuted,
      currentTrack: state.currentTrack ? {
        id: state.currentTrack.id,
        title: state.currentTrack.title,
        src: state.currentTrack.src || '',
        type: state.currentTrack.type,
        genres: state.currentTrack.genres,
        category: state.currentTrack.category,
      } : null,
      currentTime: state.currentTime,
      duration: state.duration,
      pauseFunction: pause,
      playFunction: resume,
    }

    globalAudioControl.registeredSources.set(currentSourceId, source)

    return () => {
      globalAudioControl.registeredSources.delete(currentSourceId)
    }
  }, [state, globalAudioControl, pause, resume])

  // Handle global audio coordination
  useEffect(() => {
    if (state.isPlaying && globalAudioControl.mutualExclusionEnabled) {
      // Pause other audio sources when this one starts playing
      globalAudioControl.registeredSources.forEach((source, id) => {
        if (id !== sourceId.current && source.isPlaying && source.pauseFunction) {
          source.pauseFunction()
        }
      })
      globalAudioControl.currentActiveSource = sourceId.current
    }
  }, [state.isPlaying, globalAudioControl])

  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setState(prev => ({
        ...prev,
        currentTime: audioRef.current?.currentTime || 0,
      }))
    }
  }, [])

  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setState(prev => ({
        ...prev,
        duration: audioRef.current?.duration || 0,
      }))
    }
  }, [])

  const handleEnded = useCallback(() => {
    // Auto-advance to next track
    setState(prev => {
      const nextIndex = prev.currentIndex + 1
      if (nextIndex < prev.playlist.length) {
        return { ...prev, currentIndex: nextIndex }
      } else {
        // End of playlist
        return { ...prev, isPlaying: false, currentTime: 0 }
      }
    })
  }, [])

  // Effect to handle track changes
  useEffect(() => {
    if (state.currentIndex >= 0 && state.currentIndex < state.playlist.length) {
      const track = state.playlist[state.currentIndex]
      if (track.src && track !== state.currentTrack) {
        setState(prev => ({ ...prev, currentTrack: track }))
      }
    }
  }, [state.currentIndex, state.playlist, state.currentTrack])

  // Effect to auto-play when track changes
  useEffect(() => {
    if (state.currentTrack?.src && audioRef.current) {
      // Small delay to ensure audio element is ready
      const timer = setTimeout(() => {
        if (audioRef.current) {
          audioRef.current.play().catch(console.error)
        }
      }, 50)

      return () => clearTimeout(timer)
    }
  }, [state.currentTrack?.id, state.currentTrack?.src])

  // Effect to handle audio element setup
  useEffect(() => {
    if (!state.currentTrack?.src) {
      setState(prev => ({ ...prev, isPlaying: false }))
      return
    }

    // Clean up previous audio
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
      audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)
      audioRef.current.removeEventListener("ended", handleEnded)
      audioRef.current.removeEventListener("play", () => {
        setState(prev => ({ ...prev, isPlaying: true }))
      })
      audioRef.current.removeEventListener("pause", () => {
        setState(prev => ({ ...prev, isPlaying: false }))
      })
    }

    // Create new audio element
    audioRef.current = new Audio(state.currentTrack.src)
    audioRef.current.volume = state.isMuted ? 0 : state.volume / 100

    // Define event handlers with proper cleanup
    const handlePlay = () => {
      setState(prev => ({ ...prev, isPlaying: true }))
    }

    const handlePause = () => {
      setState(prev => ({ ...prev, isPlaying: false }))
    }

    // Add event listeners
    audioRef.current.addEventListener("timeupdate", handleTimeUpdate)
    audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)
    audioRef.current.addEventListener("ended", handleEnded)
    audioRef.current.addEventListener("play", handlePlay)
    audioRef.current.addEventListener("pause", handlePause)

    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)
        audioRef.current.removeEventListener("ended", handleEnded)
        audioRef.current.removeEventListener("play", handlePlay)
        audioRef.current.removeEventListener("pause", handlePause)
      }
    }
  }, [state.currentTrack?.src, state.isMuted, state.volume, handleTimeUpdate, handleLoadedMetadata, handleEnded])

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = state.isMuted ? 0 : state.volume / 100
    }
  }, [state.volume, state.isMuted])

  const setPlaylist = useCallback((tracks: PlaylistTrack[]) => {
    setState(prev => ({
      ...prev,
      playlist: tracks,
      currentIndex: tracks.length > 0 ? 0 : -1,
    }))
  }, [])

  const playTrack = useCallback((trackId: string) => {
    setState(prev => {
      const index = prev.playlist.findIndex(track => track.id === trackId)
      if (index !== -1) {
        return { ...prev, currentIndex: index, currentTime: 0 }
      }
      return prev
    })
  }, [])

  const play = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.play()
        .then(() => {
          // State will be updated by the 'play' event listener
        })
        .catch(console.error)
    }
  }, [])

  const togglePlayPause = useCallback(() => {
    if (audioRef.current) {
      if (state.isPlaying) {
        pause()
      } else {
        play()
      }
    }
  }, [state.isPlaying, play, pause])

  const skipToNext = useCallback(() => {
    setState(prev => {
      const nextIndex = prev.currentIndex + 1
      if (nextIndex < prev.playlist.length) {
        return { ...prev, currentIndex: nextIndex }
      }
      return prev
    })
  }, [])

  const skipToPrevious = useCallback(() => {
    setState(prev => {
      const prevIndex = prev.currentIndex - 1
      if (prevIndex >= 0) {
        return { ...prev, currentIndex: prevIndex }
      }
      return prev
    })
  }, [])

  const setVolume = useCallback((volume: number) => {
    setState(prev => ({ ...prev, volume }))
  }, [])

  const toggleMute = useCallback(() => {
    setState(prev => ({ ...prev, isMuted: !prev.isMuted }))
  }, [])

  const seek = useCallback((time: number) => {
    if (audioRef.current && !isNaN(time) && isFinite(time)) {
      audioRef.current.currentTime = Math.max(0, Math.min(time, audioRef.current.duration || 0))
      // Don't manually update state here - let the timeupdate event handle it
    }
  }, [])

  const stop = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }
    setState(prev => ({
      ...prev,
      isPlaying: false,
      currentTime: 0,
      currentTrack: null,
      currentIndex: -1,
    }))
  }, [])

  return {
    // State
    currentTrack: state.currentTrack,
    isPlaying: state.isPlaying,
    volume: state.volume,
    isMuted: state.isMuted,
    currentTime: state.currentTime,
    duration: state.duration,
    playlist: state.playlist,
    currentIndex: state.currentIndex,
    
    // Actions
    setPlaylist,
    playTrack,
    play,
    pause,
    resume,
    togglePlayPause,
    skipToNext,
    skipToPrevious,
    setVolume,
    toggleMute,
    seek,
    stop,
  }
}
