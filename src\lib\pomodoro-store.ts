'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { notifyPomodoroComplete } from './ios-safe-notification';
import { useNotificationStore } from './notification-store';
import { NatureSoundCategory, UserRole } from '@prisma/client';

// Video type definition
export interface Video {
  id: string;
  title: string;
  src: string;
  thumbnail: string;
  description?: string;
  isPublic?: boolean;
  isPremium?: boolean;
  userId?: string;
  creatorType?: UserRole;
  videoGenre?: string[];
  playlistId?: string;
  isFavorite: boolean;
  order?: number;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  musicTrackId?: string; // Added musicTrackId property

  // Playlist data
  playlist?: {
    id: string;
    name: string;
    description?: string;
    imageUrl?: string | null;
    isPublic?: boolean;
    isDefault?: boolean;
    creatorType?: UserRole;
    userId?: string;
    musicOrder?: string[];

    // Music tracks in the playlist
    musics: {
      id: string;
      title: string;
      src: string | null;
      isPublic: boolean;
      creatorType: UserRole;
      userId: string;
      genres?: string[];
      createdAt: Date | string;
      updatedAt: Date | string;
    }[];

    // Nature sounds in the playlist
    natureSounds: {
      id: string;
      title: string;
      src: string | null;
      category?: NatureSoundCategory[];
      isPublic: boolean;
      creatorType: UserRole;
      userId: string;
      createdAt: Date | string;
      updatedAt: Date | string;
    }[];
  };
}

export interface TimerSettings {
  pomodoroMinutes: number;
  shortBreakMinutes: number;
  longBreakMinutes: number;
  sessionsBeforeLongBreak: number;
  longBreakInterval: number; // Add this alias for compatibility
  timerMode: 'countDown' | 'countUp'; // Add timer mode for focus sessions
}

export type TimerPhase = 'pomodoro' | 'shortBreak' | 'longBreak';

export type TimerPositions = 'center' | 'top-left' | 'top-center' | 'top-right' | 'middle-left' | 'middle-right' |
  'bottom-left' | 'bottom-left-center' | 'bottom-center' | 'bottom-right-center' | 'bottom-right';

// Update TimerColorPreset type to include new color options
export type TimerColorPreset = 'white' | 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo' | 'pink' | 'orange';

// Add TimerUIStyle type definition
export type TimerUIStyle = 'default' | 'circular';

// Add new content type definition for navigation
export type ContentType = 'video' | 'music' | 'nature';

export interface NavigableContent {
  id: string;
  title: string;
  type: ContentType;
  src: string | null;
  thumbnail?: string;
  description?: string;
  // For video content
  video?: Video;
  // For music content
  musicTrack?: {
    id: string;
    title: string;
    src: string | null;
    genres?: string[];
  };
  // For nature sound content
  natureSound?: {
    id: string;
    title: string;
    src: string | null;
    category?: NatureSoundCategory[];
  };
}

export interface PomodoroSettings {
  pomodoroMinutes: number;
  shortBreakMinutes: number;
  longBreakMinutes: number;
  longBreakInterval: number;
  sessionsBeforeLongBreak: number;
  timerMode: 'countDown' | 'countUp'; // Add timer mode for focus sessions
}

export interface PomodoroState {
  selectedVideo: Video | null;
  previousVideo: Video | null; // Track the previous video for better transitions
  videos: Video[];
  // Add navigation state
  currentContentIndex: number;
  navigableContent: NavigableContent[];
  isNavigating: boolean;
  isVideoSwitching: boolean; // Add shared video switching state
  timerSettings: PomodoroSettings;
  isRunning: boolean;
  currentPhase: TimerPhase;
  timeRemaining: number;
  completedSessions: number;
  isFullscreen: boolean;
  timerPosition: TimerPositions;
  isSelectingVideo: boolean; // Flag to track if video selection is in progress
  pomodoroCount: number; // Add this to track the current pomodoro session number
  timerColor: TimerColorPreset; // Add timer text color
  timerOpacity: number; // Add timer transparency control (0-100)
  autoStartBreaks: boolean; // Add auto start breaks setting
  autoStartPomodoros: boolean; // Add auto start pomodoros setting
  autoFullscreen: boolean; // Add auto fullscreen setting
  manuallyPaused: boolean; // Track if the timer was manually paused
  completedPomodoros: number;
  timerUIStyle: TimerUIStyle;
  isSettingsDialogOpen: boolean; // Add state to track if settings dialog is open
  sessionStartTime: Date | null; // Track when the current session started
  currentTask: { id: string; title: string } | null; // Track the current task being worked on
  showProgressBar: boolean; // Add toggle for progress bar visibility
  showCurrentTime: boolean; // Add toggle for current time display

  // Actions
  selectVideo: (video: Video) => void;
  setVideos: (videos: Video[]) => void;
  // Add navigation actions
  navigateToNext: () => void;
  navigateToPrevious: () => void;
  navigateToContent: (content: NavigableContent) => void;
  buildNavigableContent: () => void;
  getCurrentContent: () => NavigableContent | null;
  setVideoSwitching: (isSwitching: boolean) => void; // Add action to control video switching state
  updateSettings: (settings: Partial<PomodoroSettings>) => void;
  startTimer: () => void;
  pauseTimer: () => void;
  resetTimer: () => void;
  skipBreak: () => void;
  toggleFullscreen: () => void;
  setTimerPosition: (position: TimerPositions) => void;
  setTimerColor: (color: TimerColorPreset) => void; // Add function to set timer color
  setTimerOpacity: (opacity: number) => void; // Add function to set timer opacity
  setAutoStartBreaks: (autoStart: boolean) => void; // Add function to set auto start breaks
  setAutoStartPomodoros: (autoStart: boolean) => void; // Add function to set auto start pomodoros
  setAutoFullscreen: (autoFullscreen: boolean) => void; // Add function to set auto fullscreen
  setShowProgressBar: (show: boolean) => void; // Add function to toggle progress bar visibility
  setShowCurrentTime: (show: boolean) => void; // Add function to toggle current time display
  tick: () => void;
  moveToNextPhase: () => void;
  setCurrentPhase: (phase: TimerPhase) => void;
  setTimeRemaining: (seconds: number) => void;
  setIsRunning: (isRunning: boolean) => void;
  setIsPaused: (isPaused: boolean) => void;
  setIsFullscreen: (isFullscreen: boolean) => void;
  setManuallyPaused: (paused: boolean) => void;
  setPomodoroCount: (count: number) => void;
  setCompletedPomodoros: (count: number) => void;
  incrementCompletedPomodoros: () => void;
  setTimerSettings: (settings: Partial<PomodoroSettings>) => void;
  setTimerUIStyle: (style: TimerUIStyle) => void;
  setSettingsDialogOpen: (isOpen: boolean) => void; // Function to control settings dialog state
  toggleSettingsDialog: () => void; // Function to toggle settings dialog state
  recordSession: () => void; // Record that a session was completed
  resetToPomodoro: () => void; // Reset timer to pomodoro phase
  setCurrentTask: (task: { id: string; title: string } | null) => void; // Set the current task
  startFocusSession: (task: { id: string; title: string }) => void; // Start a focus session with a task
}

// Note: Custom persistence functions removed - now using Zustand persist middleware exclusively



export const usePomodoroStore = create<PomodoroState>()(
  persist(
    (set, get) => ({
      selectedVideo: null,
      previousVideo: null,
      videos: [],
      // Add navigation state
      currentContentIndex: 0,
      navigableContent: [],
      isNavigating: false,
      isVideoSwitching: false,
      timerSettings: {
        pomodoroMinutes: 25,
        shortBreakMinutes: 5,
        longBreakMinutes: 15,
        sessionsBeforeLongBreak: 4,
        longBreakInterval: 4,
        timerMode: 'countDown', // Default to count down mode for backward compatibility
      },
      isRunning: false, // Always start with timer stopped
      currentPhase: 'pomodoro', // Always start in pomodoro phase
      timeRemaining: 25 * 60, // Always start with pomodoro duration (25 minutes default)
      completedSessions: 0,
      isFullscreen: false,
      timerPosition: 'bottom-right',
      isSelectingVideo: false,
      pomodoroCount: 1, // Initialize with 1 for the first session
      timerColor: 'white', // Default timer color
      timerOpacity: 20, // Default timer opacity (20%) - Minimal preset
      autoStartBreaks: false, // Default value for auto start breaks
      autoStartPomodoros: false, // Default value for auto start pomodoros
      autoFullscreen: false, // Default value for auto fullscreen
      manuallyPaused: false, // Always start as not manually paused
      completedPomodoros: 0,
      timerUIStyle: 'default',
      isSettingsDialogOpen: false, // Initialize settings dialog as closed
      sessionStartTime: null, // Always start with no active session
      currentTask: null, // Initialize current task as null
      showProgressBar: true, // Default to showing progress bar
      showCurrentTime: false, // Default to not showing current time

      selectVideo: (video: Video) => {
        // Avoid re-selecting the same video
        if (get().selectedVideo?.id === video.id) return;

        // Set selecting flag to true
        set({ isSelectingVideo: true });

        // Store current video as previous for smooth transition
        const currentVideo = get().selectedVideo;

        // Update selected video immediately for faster transitions
        set({
          selectedVideo: video,
          previousVideo: currentVideo,
          isSelectingVideo: false
        });

        // Update current content index to match the selected video
        const navigableContent = get().navigableContent;
        const videoIndex = navigableContent.findIndex(content =>
          content.type === 'video' && content.id === video.id
        );
        if (videoIndex !== -1) {
          set({ currentContentIndex: videoIndex });
        }
      },

      setVideos: (videos: Video[]) => {
        set({ videos });
        // Build navigable content when videos are set
        get().buildNavigableContent();

        // If there's a selected video, find its index in the navigable content
        const selectedVideo = get().selectedVideo;
        if (selectedVideo) {
          const navigableContent = get().navigableContent;
          const videoIndex = navigableContent.findIndex(content =>
            content.type === 'video' && content.id === selectedVideo.id
          );
          if (videoIndex !== -1) {
            set({ currentContentIndex: videoIndex });
          }
        }
      },

      updateSettings: (settings: Partial<PomodoroSettings>) => {
        const currentSettings = get().timerSettings;
        const newSettings = { ...currentSettings, ...settings };

        set({
          timerSettings: newSettings,
          timeRemaining: get().currentPhase === 'pomodoro'
            ? newSettings.pomodoroMinutes * 60
            : get().currentPhase === 'shortBreak'
              ? newSettings.shortBreakMinutes * 60
              : newSettings.longBreakMinutes * 60
        });
        // Zustand persist middleware handles persistence automatically
      },

      startTimer: () => {
        const currentTime = new Date();

        // Set the timer running state
        set({
          isRunning: true,
          manuallyPaused: false,
          // Set session start time when starting the timer if it's not already set
          // Ensure it's always a Date object
          sessionStartTime: get().sessionStartTime instanceof Date ? get().sessionStartTime : currentTime
        });

        // Note: We no longer attempt to auto-enter fullscreen here
        // This is now handled in the TimerPage component with a user interaction prompt
        // to comply with browser security policies
      },

      pauseTimer: () => set({ isRunning: false, manuallyPaused: true }),

      resetTimer: () => {
        const { currentPhase, timerSettings } = get();
        let resetTime = 0;

        switch (currentPhase) {
          case 'pomodoro':
            resetTime = timerSettings.pomodoroMinutes * 60;
            break;
          case 'shortBreak':
            resetTime = timerSettings.shortBreakMinutes * 60;
            break;
          case 'longBreak':
            resetTime = timerSettings.longBreakMinutes * 60;
            break;
        }

        set({
          timeRemaining: resetTime,
          isRunning: false,
          manuallyPaused: true,
          sessionStartTime: null, // Reset session start time when timer is reset
          currentTask: null // Clear current task when timer is reset
        });
      },

      skipBreak: () => {
        const { currentPhase } = get();
        if (currentPhase === 'shortBreak' || currentPhase === 'longBreak') {
          // When skipping a break, we move to the next pomodoro and update the counter
          const newPomodoroCount = currentPhase === 'longBreak' ? 1 : get().pomodoroCount + 1;

          set({
            currentPhase: 'pomodoro',
            timeRemaining: get().timerSettings.pomodoroMinutes * 60,
            pomodoroCount: newPomodoroCount,
            manuallyPaused: false // Reset manually paused flag when skipping to a new phase
          });
        }
      },

      toggleFullscreen: () => set((state: PomodoroState) => ({ isFullscreen: !state.isFullscreen })),

      setTimerPosition: (position: TimerPositions) => {
        set({ timerPosition: position });
        // Zustand persist middleware handles persistence automatically
      },

      setTimerColor: (color: TimerColorPreset) => {
        set({ timerColor: color });
        // Zustand persist middleware handles persistence automatically
      },

      setTimerOpacity: (opacity: number) => {
        const clampedOpacity = Math.max(0, Math.min(100, opacity));
        set({ timerOpacity: clampedOpacity });
        // Zustand persist middleware handles persistence automatically
      },

      setAutoStartBreaks: (autoStart: boolean) => {
        set({ autoStartBreaks: autoStart });
        // Zustand persist middleware handles persistence automatically
      },

      setAutoStartPomodoros: (autoStart: boolean) => {
        set({
          autoStartPomodoros: autoStart,
          // Reset manually paused flag when changing auto start setting
          ...(autoStart ? { manuallyPaused: false } : {})
        });
        // Zustand persist middleware handles persistence automatically
      },

      setAutoFullscreen: (autoFullscreen: boolean) => {
        set({ autoFullscreen });
        // Zustand persist middleware handles persistence automatically
      },

      setShowProgressBar: (show: boolean) => {
        set({ showProgressBar: show });
        // Zustand persist middleware handles persistence automatically
      },

      setShowCurrentTime: (show: boolean) => {
        set({ showCurrentTime: show });
        // Zustand persist middleware handles persistence automatically
      },

      tick: () => {
        const { timeRemaining, isRunning } = get();

        if (isRunning && timeRemaining > 0) {
          set({ timeRemaining: timeRemaining - 1 });
        } else if (isRunning && timeRemaining === 0) {
          get().moveToNextPhase();
        }
      },

      moveToNextPhase: () => {
        const { currentPhase, completedSessions, timerSettings, autoStartBreaks, autoStartPomodoros } = get();

        // Preload notification sound for immediate playback
        if (typeof window !== 'undefined') {
          useNotificationStore.getState().preloadSound();
        }

        if (currentPhase === 'pomodoro') {
          const newCompletedSessions = completedSessions + 1;
          const isLongBreakDue = newCompletedSessions % timerSettings.sessionsBeforeLongBreak === 0;

          // Notify that Pomodoro is complete
          if (typeof window !== 'undefined') {
            notifyPomodoroComplete().catch(error => {
              console.error('Error showing pomodoro completion notification:', error);
            });
          }

          // Don't clear sessionStartTime first - let the recording function handle it
          // This ensures the session is properly recorded before the state is changed

          // Set the state changes for the break
          set({
            completedSessions: newCompletedSessions,
            currentPhase: isLongBreakDue ? 'longBreak' : 'shortBreak',
            timeRemaining: isLongBreakDue
              ? timerSettings.longBreakMinutes * 60
              : timerSettings.shortBreakMinutes * 60,
            isRunning: autoStartBreaks, // Only auto-start if autoStartBreaks is true
            manuallyPaused: false, // Reset manually paused flag when changing phases
            // Start a new session timer for break tracking if auto-starting
            sessionStartTime: autoStartBreaks ? new Date() : null
            // Keep current task during breaks - don't clear it here
          });
        } else {
          // When transitioning from break back to pomodoro
          const isLongBreakComplete = currentPhase === 'longBreak';
          
          set({
            currentPhase: 'pomodoro',
            timeRemaining: timerSettings.pomodoroMinutes * 60,
            isRunning: autoStartPomodoros, // Only auto-start if autoStartPomodoros is true
            pomodoroCount: isLongBreakComplete ? 1 : get().pomodoroCount + 1,
            manuallyPaused: false, // Reset manually paused flag when changing phases
            // Start a new session timer when transitioning to a pomodoro
            sessionStartTime: autoStartPomodoros ? new Date() : null,
            // Clear current task after completing a long break cycle
            currentTask: isLongBreakComplete ? null : get().currentTask
          });
        }
      },

      setCurrentPhase: (phase: TimerPhase) => set({ currentPhase: phase }),

      setTimeRemaining: (seconds: number) => set({ timeRemaining: seconds }),

      setIsRunning: (isRunning: boolean) => set({ isRunning }),

      setIsPaused: (isPaused: boolean) => set({ manuallyPaused: isPaused }),

      setIsFullscreen: (isFullscreen: boolean) => set({ isFullscreen }),

      setManuallyPaused: (paused: boolean) => set({ manuallyPaused: paused }),

      setPomodoroCount: (count: number) => set({ pomodoroCount: count }),

      setCompletedPomodoros: (count: number) => set({ completedPomodoros: count }),

      incrementCompletedPomodoros: () => {
        const { completedPomodoros } = get();
        set({ completedPomodoros: completedPomodoros + 1 });
      },

      setTimerSettings: (settings: Partial<PomodoroSettings>) => {
        const currentSettings = get().timerSettings;
        const newSettings = { ...currentSettings, ...settings };
        set({
          timerSettings: newSettings,
          timeRemaining: get().currentPhase === 'pomodoro'
            ? newSettings.pomodoroMinutes * 60
            : get().currentPhase === 'shortBreak'
              ? newSettings.shortBreakMinutes * 60
              : newSettings.longBreakMinutes * 60
        });
        // Zustand persist middleware handles persistence automatically
      },

      setTimerUIStyle: (style: TimerUIStyle) => {
        set({ timerUIStyle: style });
        // Zustand persist middleware handles persistence automatically
      },

      setSettingsDialogOpen: (isOpen: boolean) => {
        set({ isSettingsDialogOpen: isOpen });
      },

      toggleSettingsDialog: () => {
        set((state) => ({ isSettingsDialogOpen: !state.isSettingsDialogOpen }));
      },

      // Function to record that a session was completed
      recordSession: () => {
        const { currentPhase, completedPomodoros, sessionStartTime } = get();

        // Only proceed if we have a valid session
        if (!sessionStartTime) {
          return;
        }

        console.log(`Recording ${currentPhase} session completion and resetting sessionStartTime`);

        // Track completed pomodoros count for FOCUS sessions only
        if (currentPhase === 'pomodoro') {
          set({
            completedPomodoros: completedPomodoros + 1,
            sessionStartTime: null // Reset session start time after recording
          });
        } else {
          // For break sessions, just reset the session start time
          set({
            sessionStartTime: null
          });
        }
      },

      resetToPomodoro: () => {
        const { timerSettings } = get();

        set({
          currentPhase: 'pomodoro',
          timeRemaining: timerSettings.pomodoroMinutes * 60,
          isRunning: false,
          manuallyPaused: false,
          sessionStartTime: null, // Reset session start time when resetting to pomodoro
          pomodoroCount: 1 // Reset to first pomodoro session
        });
      },

      // Add navigation actions
      navigateToNext: () => {
        const { currentContentIndex, navigableContent } = get();
        if (navigableContent.length === 0) return;

        const nextIndex = (currentContentIndex + 1) % navigableContent.length;
        const nextContent = navigableContent[nextIndex];

        set({
          currentContentIndex: nextIndex,
          isNavigating: true
        });

        // If navigating to a video, select it
        if (nextContent.type === 'video' && nextContent.video) {
          get().selectVideo(nextContent.video);
        }

        // Reset navigation flag after shorter animation for faster transitions
        setTimeout(() => {
          set({ isNavigating: false });
        }, 200);
      },

      navigateToPrevious: () => {
        const { currentContentIndex, navigableContent } = get();
        if (navigableContent.length === 0) return;

        const prevIndex = (currentContentIndex - 1 + navigableContent.length) % navigableContent.length;
        const prevContent = navigableContent[prevIndex];

        set({
          currentContentIndex: prevIndex,
          isNavigating: true
        });

        // If navigating to a video, select it
        if (prevContent.type === 'video' && prevContent.video) {
          get().selectVideo(prevContent.video);
        }

        // Reset navigation flag after shorter animation for faster transitions
        setTimeout(() => {
          set({ isNavigating: false });
        }, 200);
      },

      navigateToContent: (content: NavigableContent) => {
        const { navigableContent } = get();
        const index = navigableContent.findIndex(c => c.id === content.id && c.type === content.type);
        if (index !== -1) {
          set({
            currentContentIndex: index,
            isNavigating: true
          });

          // If navigating to a video, select it
          if (content.type === 'video' && content.video) {
            get().selectVideo(content.video);
          }

          // Reset navigation flag after shorter animation for faster transitions
          setTimeout(() => {
            set({ isNavigating: false });
          }, 200);
        }
      },

      buildNavigableContent: () => {
        const { videos } = get();
        const navigableContent: NavigableContent[] = [];

        // Add videos to navigableContent
        videos.forEach(video => {
          navigableContent.push({
            id: video.id,
            title: video.title,
            type: 'video',
            src: video.src,
            thumbnail: video.thumbnail,
            description: video.description,
            video: video,
          });
        });

        // Add music tracks to navigableContent
        videos.forEach(video => {
          video.playlist?.musics.forEach(music => {
            if (music.src) { // Only add if src is not null
              navigableContent.push({
                id: music.id,
                title: music.title,
                type: 'music',
                src: music.src,
                musicTrack: music,
              });
            }
          });
        });

        // Add nature sounds to navigableContent
        videos.forEach(video => {
          video.playlist?.natureSounds.forEach(natureSound => {
            if (natureSound.src) { // Only add if src is not null
              navigableContent.push({
                id: natureSound.id,
                title: natureSound.title,
                type: 'nature',
                src: natureSound.src,
                natureSound: natureSound,
              });
            }
          });
        });

        set({ navigableContent });
      },

      getCurrentContent: () => {
        const { currentContentIndex, navigableContent } = get();
        return navigableContent[currentContentIndex] || null;
      },

      setVideoSwitching: (isSwitching: boolean) => {
        set({ isVideoSwitching: isSwitching });
      },

      setCurrentTask: (task: { id: string; title: string } | null) => {
        set({ currentTask: task });
      },

      startFocusSession: (task: { id: string; title: string }) => {
        const currentState = get();
        
        // Set the current task
        set({ currentTask: task });
        
        // If timer is currently running
        if (currentState.isRunning) {
          // If it's a break phase, let it continue but set the task for the next pomodoro
          if (currentState.currentPhase === 'shortBreak' || currentState.currentPhase === 'longBreak') {
            // Just set the task, don't change the timer state
            return;
          }
          
          // If it's already a pomodoro phase, just switch the task and keep running
          if (currentState.currentPhase === 'pomodoro') {
            // Timer continues running with the new task
            return;
          }
        }
        
        // If timer is not running or we need to reset to pomodoro
        // Reset to pomodoro phase and start immediately
        set({
          currentPhase: 'pomodoro',
          timeRemaining: currentState.timerSettings.pomodoroMinutes * 60,
          pomodoroCount: 1,
          isRunning: true, // Start immediately
          manuallyPaused: false,
          sessionStartTime: new Date() // Set session start time since we're starting
        });
      },
    }),
    {
      name: 'pomodoro-storage',
      partialize: (state) => ({
        // Only persist settings and configuration, not runtime state
        timerSettings: state.timerSettings,
        timerPosition: state.timerPosition,
        timerColor: state.timerColor,
        timerOpacity: state.timerOpacity,
        autoStartBreaks: state.autoStartBreaks,
        autoStartPomodoros: state.autoStartPomodoros,
        autoFullscreen: state.autoFullscreen,
        timerUIStyle: state.timerUIStyle,
        completedPomodoros: state.completedPomodoros,
        showProgressBar: state.showProgressBar,
        showCurrentTime: state.showCurrentTime,
        // Don't persist runtime state like:
        // - isRunning, currentPhase, timeRemaining, sessionStartTime
        // - manuallyPaused, pomodoroCount, completedSessions
        // - selectedVideo, videos, isSelectingVideo, isFullscreen
        // - isSettingsDialogOpen, previousVideo
        // - currentContentIndex, navigableContent, isNavigating
      }),
    }
  )
);