'use client';

import { usePomodoroStore } from '@/lib/pomodoro-store';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function TimerSettingsTest() {
  const { timerSettings, updateSettings } = usePomodoroStore();

  const testSaveSettings = () => {
    // Test saving some custom settings
    updateSettings({
      pomodoroMinutes: 30,
      shortBreakMinutes: 10,
      longBreakMinutes: 20,
      sessionsBeforeLongBreak: 3,
      timerMode: 'countUp'
    });
    console.log('Test settings saved!');
  };

  const testResetSettings = () => {
    // Reset to defaults
    updateSettings({
      pomodoroMinutes: 25,
      shortBreakMinutes: 5,
      longBreakMinutes: 15,
      sessionsBeforeLongBreak: 4,
      timerMode: 'countDown'
    });
    console.log('Settings reset to defaults!');
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Timer Settings Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <p><strong>Current Settings:</strong></p>
          <ul className="text-sm space-y-1">
            <li>Pomodoro: {timerSettings.pomodoroMinutes} min</li>
            <li>Short Break: {timerSettings.shortBreakMinutes} min</li>
            <li>Long Break: {timerSettings.longBreakMinutes} min</li>
            <li>Sessions: {timerSettings.sessionsBeforeLongBreak}</li>
            <li>Mode: {timerSettings.timerMode}</li>
          </ul>
        </div>
        
        <div className="space-y-2">
          <Button onClick={testSaveSettings} className="w-full">
            Test Save (30/10/20/3/countUp)
          </Button>
          <Button onClick={testResetSettings} variant="outline" className="w-full">
            Reset to Defaults
          </Button>
        </div>
        
        <p className="text-xs text-muted-foreground">
          Save settings, refresh the page, and check if they persist.
        </p>
      </CardContent>
    </Card>
  );
}
