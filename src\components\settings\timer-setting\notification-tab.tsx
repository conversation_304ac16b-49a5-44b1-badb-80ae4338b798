'use client';

import { Bell, Volume2, VolumeX, RotateCcw, PlayCircle, AlertCircle, Settings, CheckCircle, BellRing } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useNotificationStore, SoundType } from '@/lib/notification-store';
import { notificationService } from '@/lib/notification-service';
import { useEffect, useState } from 'react';

import { cn } from '@/lib/utils';

export function NotificationTab() {
  // Common section class for consistent spacing and styling
  const sectionClass = "space-y-1.5 mb-3";
  const headerClass = "flex items-center justify-between mb-1.5";
  const titleClass = "text-xs font-medium flex items-center gap-1.5 text-foreground/80";
  const descriptionClass = "text-[10px] text-muted-foreground leading-tight";

  // Permission state
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  // Get notification settings from store
  const {
    settings,
    setSoundType,
    setVolume,
    setRepeat,
    setEnabled,
    playTestSound,
    preloadSound
  } = useNotificationStore();

  // Check permission status on component mount and when settings change
  useEffect(() => {
    if (notificationService) {
      const currentStatus = notificationService.getPermissionStatus();
      setPermissionStatus(currentStatus);
    }
  }, []);

  // Preload sound when component mounts
  useEffect(() => {
    if (settings.enabled) {
      preloadSound();
    }
  }, [preloadSound, settings.enabled]);

  // Sound options for the select dropdown
  const soundOptions: {value: SoundType; label: string; description?: string}[] = [
    { value: 'bell', label: 'Bell', description: 'Classic bell notification' },
    { value: 'check', label: 'Check', description: 'Success confirmation sound' },
    { value: 'elevator', label: 'Elevator', description: 'Gentle elevator ding' },
    { value: 'funny', label: 'Funny', description: 'Playful notification sound' },
    { value: 'level', label: 'Level Up', description: 'Achievement completion sound' },
    { value: 'ping', label: 'Ping', description: 'Simple ping notification' },
    { value: 'positive', label: 'Positive', description: 'Uplifting notification tone' },
    { value: 'simple', label: 'Simple', description: 'Clean and minimal alert' }
  ];

  // Handle requesting notification permission
  const handleRequestPermission = async () => {
    if (!notificationService) return;

    setIsRequestingPermission(true);
    try {
      const granted = await notificationService.requestPermission();
      const newStatus = notificationService.getPermissionStatus();
      setPermissionStatus(newStatus);

      if (granted) {
        console.log('Notification permission granted');
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    } finally {
      setIsRequestingPermission(false);
    }
  };

  // Handle playing test sound
  const handlePlayTest = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); // Prevent default form submission
    e.stopPropagation(); // Stop event propagation

    playTestSound();
  };

  // Handle sound type change with automatic playback
  const handleSoundTypeChange = (value: string) => {
    const soundType = value as SoundType;

    // Set the new sound type
    setSoundType(soundType);

    // Only auto-play if notifications are enabled
    if (settings.enabled) {
      // Play the sound after a brief delay to ensure it's loaded
      setTimeout(() => {
        playTestSound();
      }, 100);
    }
  };

  return (
    <div className="space-y-3">
      {/* Enable Notifications Toggle */}
      <div className={sectionClass}>
          <div className={headerClass}>
            <h3 className={titleClass}>
              <Bell className="h-3 w-3 text-primary/70" />
              <span>Enable Notifications</span>
            </h3>
            <Switch
              size="sm"
              checked={settings.enabled}
              onCheckedChange={setEnabled}
              className="data-[state=checked]:bg-primary"
            />
          </div>
          <p className={descriptionClass}>
            Play sound notifications when timer completes
          </p>
        </div>

        {/* Sound Type Selection */}
        <div className={sectionClass}>
          <div className={headerClass}>
            <h3 className={titleClass}>
              <Settings className="h-3 w-3 text-primary/70" />
              <span>Sound Type</span>
            </h3>
            <Badge variant="outline" className="h-4 min-w-[35px] px-1.5 text-[10px] font-normal text-center bg-muted/30 border-border/50 capitalize">
              {settings.soundType}
            </Badge>
          </div>
          <div className="flex items-center gap-2 mt-1.5">
            <Select
              value={settings.soundType}
              onValueChange={handleSoundTypeChange}
              disabled={!settings.enabled}
            >
              <SelectTrigger className="h-6 text-xs bg-background/50 border-border/50 flex-1 hover:bg-background/80 hover:border-border/70">
                <SelectValue placeholder="Select a sound" />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {soundOptions.map((option) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className="text-xs py-1.5 cursor-pointer hover:bg-primary/10 hover:text-primary focus:bg-primary/15 focus:text-primary"
                  >
                    <span className="font-medium">{option.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 bg-background/50 border-border/50 hover:bg-primary/10 hover:border-primary/30"
              onClick={handlePlayTest}
              disabled={!settings.enabled}
              type="button"
            >
              <PlayCircle className="h-4 w-4 text-primary/70" />
            </Button>
          </div>
        </div>

        {/* Volume Control */}
        <div className={sectionClass}>
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-1.5">
              <Volume2 className="h-3 w-3 text-primary/70" />
              <span className="text-xs font-medium text-foreground/80">Volume</span>
            </div>
            <div className="flex items-center gap-2.5 flex-1 max-w-[200px]">
              <VolumeX className="h-3 w-3 text-muted-foreground" />
              <Slider
                value={[settings.volume]}
                min={0}
                max={100}
                step={5}
                disabled={!settings.enabled}
                onValueChange={([value]) => setVolume(value)}
                className={cn(
                  "flex-1",
                  !settings.enabled && "opacity-50"
                )}
              />
              <Volume2 className="h-3 w-3 text-muted-foreground" />
              <Badge variant="outline" className="h-4 min-w-[35px] px-1.5 text-[10px] font-normal text-center bg-muted/30 border-border/50">
                {settings.volume}%
              </Badge>
            </div>
          </div>
        </div>

        {/* Repeat Count */}
        <div className={sectionClass}>
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-1.5">
              <RotateCcw className="h-3 w-3 text-primary/70" />
              <span className="text-xs font-medium text-foreground/80">Repeat Count</span>
            </div>
            <div className="flex items-center gap-2.5">
              <Input
                type="number"
                min={1}
                max={10}
                value={settings.repeat}
                onChange={(e) => {
                  const value = parseInt(e.target.value, 10);
                  if (!isNaN(value) && value >= 1 && value <= 10) {
                    setRepeat(value);
                  }
                }}
                disabled={!settings.enabled}
                className="w-16 h-7 text-center text-xs bg-background/50 border-border/50"
              />
              <Badge variant="outline" className="h-4 min-w-[30px] px-1.5 text-[10px] font-normal text-center bg-muted/30 border-border/50">
                {settings.repeat}x
              </Badge>
            </div>
          </div>
          <div className="mt-1">
            <p className="text-[10px] text-muted-foreground">
              Times to play notification sound (1-10)
            </p>
          </div>
        </div>

        {/* Browser Notification Permission */}
        <div className="mt-4 pt-3 border-t border-border/40">
          <div className="space-y-2">
            <h4 className="text-[11px] font-medium text-foreground/90 flex items-center gap-1.5">
              <Bell className="h-3 w-3 text-primary/70" />
              Browser Notification Permission
            </h4>

            {permissionStatus === 'granted' ? (
              <div className="flex items-center gap-2 px-2 py-1.5 rounded-sm bg-green-50/50 border border-green-200/50 dark:bg-green-950/20 dark:border-green-800/30">
                <CheckCircle className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                <div className="flex-1">
                  <p className="text-[10px] font-medium text-green-700 dark:text-green-300">
                    Permission Granted
                  </p>
                  <p className="text-[9px] text-green-600/80 dark:text-green-400/80">
                    Notifications are enabled for this application
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-start gap-2 px-2 py-1.5 rounded-sm bg-amber-50/50 border border-amber-200/50 dark:bg-amber-950/20 dark:border-amber-800/30">
                  <AlertCircle className="h-3.5 w-3.5 text-amber-600 dark:text-amber-400 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-[10px] text-amber-700 dark:text-amber-300 leading-tight">
                      {permissionStatus === 'denied'
                        ? 'Notifications are blocked. Please enable them in your browser settings.'
                        : 'Enable browser notifications to receive alerts when your timer completes.'
                      }
                    </p>
                  </div>
                </div>

                {permissionStatus !== 'denied' && (
                  <Button
                    onClick={handleRequestPermission}
                    disabled={isRequestingPermission}
                    className="w-full h-8 text-xs bg-primary hover:bg-primary/90"
                  >
                    <BellRing className="h-3 w-3 mr-1.5" />
                    {isRequestingPermission ? 'Requesting...' : 'Enable Notifications'}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
    </div>
  );
}
