---
description:
globs:
alwaysApply: false
---
# API Routes

## Next.js Route Handlers
API endpoints are implemented as Route Handlers in the [src/app/api](mdc:src/app/api) directory using the Next.js App Router.

## Hono Integration
The application uses Hono for enhanced API functionality. API definitions can be found in [src/server](mdc:src/server).

## Main API Endpoints
- `/api/auth/*` - Authentication endpoints
- `/api/timer/*` - Timer management endpoints
- `/api/tasks/*` - Task management endpoints
- `/api/user/*` - User profile and settings endpoints
- `/api/payments/*` - Subscription and payment endpoints

## API Implementation Pattern
- Route handlers follow REST conventions
- Request validation with Zod schemas
- Typed responses for better frontend integration
- Error handling with appropriate HTTP status codes

## API Usage
API endpoints are consumed by client components using React Query and the Hono client for type safety.
