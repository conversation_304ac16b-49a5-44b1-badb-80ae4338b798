---
description:
globs:
alwaysApply: false
---
# App Router Structure

## Main Routes
The application is structured using Next.js App Router with the following main routes:
- [src/app/(main)](mdc:src/app/(main)) - Main authenticated pages
- [src/app/timer](mdc:src/app/timer) - Timer interface
- [src/app/dashboard](mdc:src/app/dashboard) - User dashboard
- [src/app/auth](mdc:src/app/auth) - Authentication pages
- [src/app/api](mdc:src/app/api) - API routes
- [src/app/admin](mdc:src/app/admin) - Admin panel

## Layout Structure
- [src/app/layout.tsx](mdc:src/app/layout.tsx) - Root layout with providers
- Route groups like `(main)` manage shared layouts

## Page Components
Each route directory contains:
- `page.tsx` - The main page component
- `layout.tsx` - (Optional) Layout wrapper
- `loading.tsx` - (Optional) Loading state
- `error.tsx` - (Optional) Error handling

## Special Files
- [src/app/globals.css](mdc:src/app/globals.css) - Global styles
- [src/app/error.tsx](mdc:src/app/error.tsx) - Global error boundary

## Routing Patterns
- Dynamic routes use folder naming like `[id]`
- Route groups with `(groupName)` share layouts without affecting URL structure
- Parallel routes with `@routeName` for complex layouts
