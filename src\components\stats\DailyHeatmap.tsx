"use client"

import { useRef } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface DailyHeatmapProps {
  data: Array<{
    hour: number
    day: string
    value: number
  }>
}

export function DailyHeatmap({ data }: DailyHeatmapProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // Get unique days and sort them
  const days = Array.from(new Set(data.map((item) => item.day)))

  // Get all hours (0-23)
  const hours = Array.from({ length: 24 }, (_, i) => i)

  // Function to get color based on value
  const getColor = (value: number) => {
    if (value === 0) return "bg-muted"
    if (value < 15) return "bg-primary/20"
    if (value < 30) return "bg-primary/40"
    if (value < 45) return "bg-primary/60"
    if (value < 60) return "bg-primary/80"
    return "bg-primary"
  }

  // Format hour for display
  const formatHour = (hour: number) => {
    const ampm = hour >= 12 ? "PM" : "AM"
    const h = hour % 12 || 12
    return `${h} ${ampm}`
  }

  return (
    <TooltipProvider delayDuration={200}>
      <div className="w-full overflow-x-auto overflow-y-visible heatmap-scroll">
        <div ref={containerRef} className="min-w-[600px] sm:min-w-[750px] lg:min-w-full relative pb-16">
          {/* Hour labels */}
          <div className="mb-4 flex">
            <div className="w-12 sm:w-16 md:w-20 flex-shrink-0"></div>
            <div className="flex-1 grid grid-cols-24 gap-0.5 sm:gap-1">
              {hours.map((hour) => (
                <div
                  key={hour}
                  className="text-center text-[10px] sm:text-xs font-medium text-muted-foreground"
                >
                  {hour}
                </div>
              ))}
            </div>
          </div>

          {/* Heatmap grid */}
          <div className="relative space-y-0.5 sm:space-y-1">
            {days.map((day) => (
              <div key={day} className="flex items-center">
                <div className="w-12 sm:w-16 md:w-20 flex-shrink-0 text-xs sm:text-sm font-medium text-muted-foreground pr-2 sm:pr-3 text-right">{day}</div>
                <div className="flex-1 grid grid-cols-24 gap-0.5 sm:gap-1">
                  {hours.map((hour) => {
                    const cellData = data.find((d) => d.day === day && d.hour === hour)
                    const value = cellData ? cellData.value : 0

                    return (
                      <Tooltip key={`${day}-${hour}`}>
                        <TooltipTrigger asChild>
                          <div
                            className={`relative h-5 sm:h-7 md:h-8 rounded-sm ${getColor(value)} transition-all duration-200 hover:opacity-80 hover:scale-105 cursor-pointer border border-background/20 touch-manipulation`}
                          >
                          </div>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          sideOffset={8}
                          avoidCollisions={true}
                          collisionPadding={20}
                          hideWhenDetached={true}
                          sticky="always"
                          className="rounded-xl border border-slate-200/60 dark:border-slate-800/60 bg-gradient-to-br from-white via-white to-slate-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30 p-3 text-center text-xs shadow-xl shadow-slate-900/10 dark:shadow-slate-900/30 backdrop-blur-md max-w-40 z-50"
                        >
                          <p className="font-semibold text-slate-900 dark:text-slate-100 mb-1">
                            {day}, {formatHour(hour)}
                          </p>
                          <p className="text-rose-600 dark:text-rose-400 font-medium">
                            {value} minutes of focus
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>

          {/* Legend */}
          <div className="mt-6 sm:mt-8 flex flex-col sm:flex-row items-center justify-center sm:justify-end gap-2 sm:gap-3">
            <div className="text-xs font-medium text-muted-foreground">Focus Intensity:</div>
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 sm:h-4 sm:w-4 rounded-sm bg-muted border border-background/20"></div>
              <div className="h-3 w-3 sm:h-4 sm:w-4 rounded-sm bg-primary/20 border border-background/20"></div>
              <div className="h-3 w-3 sm:h-4 sm:w-4 rounded-sm bg-primary/40 border border-background/20"></div>
              <div className="h-3 w-3 sm:h-4 sm:w-4 rounded-sm bg-primary/60 border border-background/20"></div>
              <div className="h-3 w-3 sm:h-4 sm:w-4 rounded-sm bg-primary/80 border border-background/20"></div>
              <div className="h-3 w-3 sm:h-4 sm:w-4 rounded-sm bg-primary border border-background/20"></div>
            </div>
            <div className="flex items-center gap-2 text-xs font-medium text-muted-foreground">
              <span>Low</span>
              <span>High</span>
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
