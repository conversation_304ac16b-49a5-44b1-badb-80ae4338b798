import { PlaylistDetailView } from "./_components/playlist-detail-view"

interface PlaylistPageProps {
  params: Promise<{
    playlistId: string
  }>
}

export default async function PlaylistPage({ params }: PlaylistPageProps) {
  const { playlistId } = await params

  return (
    <div className="flex-1 overflow-y-auto overflow-x-hidden">
      <div className="w-full max-w-[90rem] mx-auto p-4">
        <PlaylistDetailView playlistId={playlistId} />
      </div>
    </div>
  )
}