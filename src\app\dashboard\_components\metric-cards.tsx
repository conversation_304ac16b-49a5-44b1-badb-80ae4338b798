"use client"

import { Card, CardContent } from "@/components/ui/card"
import { TrendingUp, TrendingDown, Clock, Target, Flame, Timer } from "lucide-react"
import { cn } from "@/lib/utils"

interface MetricCardData {
  id: string
  title: string
  value: string
  change: string
  changeType: "increase" | "decrease"
  icon: React.ReactNode
  bgColor: string
  iconBg: string
}

interface MetricCardsProps {
  focusTimeToday: number
  completedSessions: number
  currentStreak: number
  totalFocusTime: number
  // Add comparison data for accurate percentage calculations
  previousFocusTime?: number
  previousCompletedSessions?: number
  previousStreak?: number
  previousTotalFocusTime?: number
}

export function MetricCards({
  focusTimeToday,
  completedSessions,
  currentStreak,
  totalFocusTime,
  previousFocusTime = 0,
  previousCompletedSessions = 0,
  previousStreak = 0,
  previousTotalFocusTime = 0
}: MetricCardsProps) {

  // Format focus time (in seconds) to hours and minutes
  const formatFocusTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const formatTotalTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    // Handle zero time
    if (seconds === 0) {
      return "0m"
    }

    // Handle less than 1 hour - show only minutes
    if (hours === 0) {
      return `${minutes}m`
    }

    // Handle exactly 1+ hours - show hours and minutes if minutes > 0
    if (minutes > 0) {
      return `${hours}h ${minutes}m`
    }

    // Handle exact hours (no remaining minutes)
    return `${hours}h`
  }

  // Calculate percentage changes
  const calculatePercentageChange = (current: number, previous: number): { change: string; changeType: "increase" | "decrease" } => {
    if (previous === 0) {
      return current > 0 ? { change: "+100%", changeType: "increase" } : { change: "0%", changeType: "increase" }
    }

    const percentChange = ((current - previous) / previous) * 100
    const isIncrease = percentChange >= 0
    const formattedChange = `${isIncrease ? "+" : ""}${Math.round(percentChange * 10) / 10}%`

    return {
      change: formattedChange,
      changeType: isIncrease ? "increase" : "decrease"
    }
  }

  // Calculate changes for each metric
  const focusTimeChange = calculatePercentageChange(focusTimeToday, previousFocusTime)
  const sessionsChange = calculatePercentageChange(completedSessions, previousCompletedSessions)
  const streakChange = calculatePercentageChange(currentStreak, previousStreak)
  const totalTimeChange = calculatePercentageChange(totalFocusTime, previousTotalFocusTime)

  const metricsData: MetricCardData[] = [
    {
      id: "focus-time",
      title: "Today's Focus",
      value: formatFocusTime(focusTimeToday),
      change: focusTimeChange.change,
      changeType: focusTimeChange.changeType,
      icon: <Clock className="h-6 w-6" />,
      bgColor: "bg-gradient-to-br from-white via-white to-red-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-red-950/20",
      iconBg: "bg-red-500"
    },
    {
      id: "sessions",
      title: "Focus Sessions",
      value: completedSessions.toString(),
      change: sessionsChange.change,
      changeType: sessionsChange.changeType,
      icon: <Target className="h-6 w-6" />,
      bgColor: "bg-gradient-to-br from-white via-white to-slate-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30",
      iconBg: "bg-slate-600"
    },
    {
      id: "streak",
      title: "Current Streak",
      value: `${currentStreak}d`,
      change: streakChange.change,
      changeType: streakChange.changeType,
      icon: <Flame className="h-6 w-6" />,
      bgColor: "bg-gradient-to-br from-white via-white to-orange-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-orange-950/20",
      iconBg: "bg-orange-500"
    },
    {
      id: "total-time",
      title: "Total Focus",
      value: formatTotalTime(totalFocusTime),
      change: totalTimeChange.change,
      changeType: totalTimeChange.changeType,
      icon: <Timer className="h-6 w-6" />,
      bgColor: "bg-gradient-to-br from-white via-white to-rose-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-rose-950/20",
      iconBg: "bg-rose-500"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-2">
      {metricsData.map((metric) => (
        <Card
          key={metric.id}
          className={cn(
            "relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-200",
            metric.bgColor
          )}
        >
          <CardContent className="px-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  {metric.title}
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {metric.value}
                </p>
                <div className="flex items-center space-x-1">
                  {metric.changeType === "increase" ? (
                    <TrendingUp className="h-3 w-3 text-green-600" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-600" />
                  )}
                  <span className={cn(
                    "text-xs font-medium",
                    metric.changeType === "increase" ? "text-green-600" : "text-red-600"
                  )}>
                    {metric.change}
                  </span>
                  <span className="text-xs text-muted-foreground">vs last week</span>
                </div>
              </div>
              <div className={cn(
                "rounded-full p-3 text-white",
                metric.iconBg
              )}>
                {metric.icon}
              </div>
            </div>

            {/* Decorative wave pattern */}
            <div className="absolute bottom-0 right-0 opacity-10">
              <svg width="100" height="60" viewBox="0 0 100 60" fill="none">
                <path
                  d="M0 30C20 10, 40 50, 60 30C80 10, 100 50, 120 30V60H0V30Z"
                  fill="currentColor"
                />
              </svg>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
