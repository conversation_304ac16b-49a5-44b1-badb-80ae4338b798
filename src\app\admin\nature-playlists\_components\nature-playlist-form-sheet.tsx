"use client";

import { useEffect, useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetD<PERSON><PERSON>,
  SheetFooter
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { 
  useCreateAdminNaturePlaylist, 
  useUpdateAdminNaturePlaylist, 
  useGetAdminNaturePlaylist 
} from "@schemas/NaturalPlaylist/natural-playlist-admin-query";

interface NaturePlaylistFormSheetProps {
  playlistId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function NaturePlaylistFormSheet({ 
  playlistId, 
  open, 
  onO<PERSON>Chang<PERSON>,
  onSuccess
}: NaturePlaylistFormSheetProps) {
  const isEditMode = !!playlistId;
  const title = isEditMode ? "Edit Nature Playlist" : "Create Nature Playlist";
  
  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    imageUrl: "",
    isPublic: true,
  });
  
  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Mutations for creating and updating playlists
  const createNaturePlaylistMutation = useCreateAdminNaturePlaylist();
  const updateNaturePlaylistMutation = useUpdateAdminNaturePlaylist();
  
  // For edit mode, fetch the playlist details
  const naturePlaylistQuery = useGetAdminNaturePlaylist(playlistId);
  
  // When in edit mode and playlist data is loaded, populate the form
  useEffect(() => {
    if (isEditMode && naturePlaylistQuery.data) {
      setFormData({
        name: naturePlaylistQuery.data.name,
        description: naturePlaylistQuery.data.description || "",
        imageUrl: naturePlaylistQuery.data.imageUrl || "",
        isPublic: naturePlaylistQuery.data.isPublic,
      });
    }
  }, [isEditMode, naturePlaylistQuery.data]);
  
  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  
  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }
    
    if (formData.imageUrl && formData.imageUrl.trim()) {
      try {
        new URL(formData.imageUrl);
      } catch {
        newErrors.imageUrl = "Please enter a valid URL";
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (isEditMode && playlistId) {
      updateNaturePlaylistMutation.mutate(
        {
          form: {
            name: formData.name,
            description: formData.description || undefined,
            imageUrl: formData.imageUrl || undefined,
            isPublic: formData.isPublic.toString(),
          },
          param: { id: playlistId },
        },
        {
          onSuccess: () => {
            toast.success("Nature playlist updated successfully");
            onSuccess?.();
          },
          onError: (error) => {
            toast.error(`Failed to update nature playlist: ${error.message}`);
          },
        }
      );
    } else {
      createNaturePlaylistMutation.mutate(
        {
          form: {
            name: formData.name,
            description: formData.description || undefined,
            imageUrl: formData.imageUrl || undefined,
            isPublic: formData.isPublic.toString(),
          },
        },
        {
          onSuccess: () => {
            onSuccess?.();
            // Reset form
            setFormData({
              name: "",
              description: "",
              imageUrl: "",
              isPublic: true,
            });
          },
          onError: (error) => {
            toast.error(`Failed to create nature playlist: ${error.message}`);
          },
        }
      );
    }
  };
  
  // Check if loading or submitting
  const isLoading = 
    (isEditMode && naturePlaylistQuery.isLoading) || 
    createNaturePlaylistMutation.isPending || 
    updateNaturePlaylistMutation.isPending;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md md:max-w-lg lg:max-w-xl w-full p-0 focus:outline-none">
        <div className="h-full flex flex-col">
          <SheetHeader className="px-6 pt-6 pb-2">
            <SheetTitle className="text-xl font-semibold">{title}</SheetTitle>
            <SheetDescription>
              {isEditMode 
                ? "Update your nature playlist details below"
                : "Fill in the details to create a new nature playlist"
              }
            </SheetDescription>
          </SheetHeader>
          
          <div className="flex-1 overflow-y-auto px-6">
            <form id="nature-playlist-form" onSubmit={handleSubmit} className="space-y-5 py-4">
              <div className="space-y-2">
                <Label htmlFor="name" className={errors.name ? 'text-destructive font-medium' : 'font-medium'}>
                  Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter nature playlist name"
                  value={formData.name}
                  onChange={handleChange}
                  className={errors.name ? 'border-destructive' : ''}
                  aria-required="true"
                  autoComplete="off"
                />
                {errors.name && (
                  <p className="text-sm text-destructive mt-1">{errors.name}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description" className="font-medium">
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Enter nature playlist description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="imageUrl" className={errors.imageUrl ? 'text-destructive font-medium' : 'font-medium'}>
                  Cover Image URL
                </Label>
                <Input
                  id="imageUrl"
                  name="imageUrl"
                  placeholder="https://example.com/image.jpg"
                  value={formData.imageUrl}
                  onChange={handleChange}
                  className={errors.imageUrl ? 'border-destructive' : ''}
                  autoComplete="off"
                />
                {errors.imageUrl && (
                  <p className="text-sm text-destructive mt-1">{errors.imageUrl}</p>
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="isPublic" className="font-medium">
                  Public Playlist
                </Label>
                <Switch
                  id="isPublic"
                  checked={formData.isPublic}
                  onCheckedChange={(checked) => handleSwitchChange('isPublic', checked === true)}
                />
              </div>
            </form>
          </div>
          
          <SheetFooter className="px-6 py-4 border-t">
            <Button 
              type="submit" 
              form="nature-playlist-form"
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  {isEditMode ? 'Update Nature Playlist' : 'Create Nature Playlist'}
                </>
              )}
            </Button>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
} 