---
description: 
globs: 
alwaysApply: false
---
# Code Conventions

## TypeScript
- Strict type checking is enabled
- No use of `any` type - prefer proper typing
- Use interfaces for object shapes
- Use type assertions sparingly and only when necessary

## React Patterns
- Functional components with hooks
- Server Components by default, Client Components only where needed
- Component props should have explicit TypeScript interfaces
- Use of React context for shared state across components

## File Structure
- Folder-based organization by feature
- Consistent naming conventions
  - camelCase for files (except components)
  - PascalCase for component files and classes
  - kebab-case for directories

## Styling
- Tailwind CSS for styling
- CSS modules for component-specific styles
- Mobile-first responsive design
- Component library theming with next-themes

## Error Handling
- Use error boundaries for component-level error handling
- Consistent error patterns in API responses
- Client-side error notifications via Sonner toast library

## Data Fetching
- React Query for client-side data fetching
- Server actions for server-side operations where appropriate
- Appropriate loading states for async operations

