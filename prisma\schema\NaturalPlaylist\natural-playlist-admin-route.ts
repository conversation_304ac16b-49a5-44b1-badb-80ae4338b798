import { <PERSON><PERSON> } from "hono";
import { z<PERSON><PERSON><PERSON><PERSON> } from "@hono/zod-validator";
import { createNaturePlaylistSchema, updateNaturePlaylistSchema } from "./nature-playlist-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { UserRole } from "@prisma/client";
import { z } from "zod";
import { adminMiddleware } from "@/server/private/middleware";

const app = new Hono<{ Variables: UserVariable }>()
  // Get all nature playlists (Admin can see all)
  .get("/", adminMiddleware, async (c) => {
    const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;
    const isDefault = c.req.query("isDefault") === "true" ? true : c.req.query("isDefault") === "false" ? false : undefined;

    const filters: Record<string, unknown> = {};

    // Add filters based on query parameters
    if (isPublic !== undefined) {
      filters.isPublic = isPublic;
    }

    if (isDefault !== undefined) {
      filters.isDefault = isDefault;
    }

    // Admin can access all playlists
    const naturePlaylists = await prisma.naturePlaylist.findMany({
      where: filters,
      include: {
        videos: {
          select: {
            id: true,
            title: true,
            thumbnail: true
          }
        },
        natureSounds: {
          select: {
            id: true,
            title: true,
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
    });

    return c.json({ data: naturePlaylists });
  })

  // Get single nature playlist (Admin can access any)
  .get("/:id", adminMiddleware, async (c) => {
    const id = c.req.param("id");

    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id },
      include: {
        videos: true,
        natureSounds: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    return c.json({ data: naturePlaylist });
  })

  // Create nature playlist (Admin)
  .post("/", adminMiddleware, zValidator("form", createNaturePlaylistSchema), async (c) => {
    const user = c.get("user");
    const { name, description, isPublic, imageUrl, videoIds, natureSoundIds } = c.req.valid("form");

    // Create relationships with videos and nature sounds if provided
    const connectVideos = videoIds && videoIds.length > 0 
      ? { connect: videoIds.map(id => ({ id })) } 
      : undefined;
      
    const connectNatureSounds = natureSoundIds && natureSoundIds.length > 0 
      ? { connect: natureSoundIds.map(id => ({ id })) } 
      : undefined;

    // Create nature playlist in database
    const naturePlaylist = await prisma.naturePlaylist.create({
      data: {
        name,
        description,
        isPublic: isPublic ?? false,
        imageUrl: imageUrl || "",
        userId: user.id,
        creatorType: UserRole.admin, // Always set as ADMIN for admin-created playlists
        videos: connectVideos,
        natureSounds: connectNatureSounds
      },
      include: {
        videos: true,
        natureSounds: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: naturePlaylist });
  })

  // Update nature playlist (Admin can update any)
  .patch("/:id", adminMiddleware, zValidator("form", updateNaturePlaylistSchema), async (c) => {
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // Check if nature playlist exists
    const existingNaturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id },
    });

    if (!existingNaturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Prepare update data with proper typing
    const updateData = {
      ...(updates.name !== undefined && { name: updates.name }),
      ...(updates.description !== undefined && { description: updates.description }),
      ...(updates.isPublic !== undefined && { isPublic: updates.isPublic }),
      ...(updates.imageUrl !== undefined && { imageUrl: updates.imageUrl }),
    };

    // Handle video and nature sound connections/disconnections
    let relationshipUpdates = {};
    
    // Videos
    if (updates.videoIds !== undefined) {
      relationshipUpdates = {
        ...relationshipUpdates,
        videos: {
          ...(updates.videoIds.length > 0 
            ? { set: updates.videoIds.map(id => ({ id })) }
            : { set: [] })
        }
      };
    }
    
    // Nature sounds
    if (updates.natureSoundIds !== undefined) {
      relationshipUpdates = {
        ...relationshipUpdates,
        natureSounds: {
          ...(updates.natureSoundIds.length > 0 
            ? { set: updates.natureSoundIds.map(id => ({ id })) }
            : { set: [] })
        }
      };
    }

    // Update the nature playlist
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id },
      data: {
        ...updateData,
        ...relationshipUpdates
      },
      include: {
        videos: true,
        natureSounds: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Delete nature playlist (Admin can delete any except defaults)
  .delete("/:id", adminMiddleware, async (c) => {
    const id = c.req.param("id");

    // Find the nature playlist
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Check if this is a default playlist which shouldn't be deleted
    if (naturePlaylist.isDefault) {
      return c.json({ error: "Cannot delete a default nature playlist" }, 403);
    }

    // Delete nature playlist
    await prisma.naturePlaylist.delete({
      where: { id },
    });

    return c.json({ success: true });
  })

  // Nature Sounds Management (Admin)
  .post("/:id/nature-sounds", adminMiddleware, zValidator("json", z.object({
    natureSoundIds: z.array(z.string())
  })), async (c) => {
    const naturePlaylistId = c.req.param("id");
    const { natureSoundIds } = c.req.valid("json");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Get current natureSoundOrder
    const currentNatureSoundOrder = naturePlaylist.natureSoundOrder || [];

    // Add new nature sound IDs to the end of the order
    const newNatureSoundOrder = [...currentNatureSoundOrder, ...natureSoundIds];

    // Add nature sound items to playlist and update natureSoundOrder
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        natureSounds: {
          connect: natureSoundIds.map(id => ({ id }))
        },
        natureSoundOrder: newNatureSoundOrder
      },
      include: {
        natureSounds: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Reorder nature sounds in playlist (Admin)
  .patch("/:id/nature-sounds/reorder", adminMiddleware, zValidator("json", z.object({
    natureSoundOrder: z.array(z.string())
  })), async (c) => {
    const naturePlaylistId = c.req.param("id");
    const { natureSoundOrder } = c.req.valid("json");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
      include: {
        natureSounds: {
          select: { id: true }
        }
      }
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Validate that all nature sound IDs in the new order exist in the playlist
    const naturePlaylistSoundIds = new Set(naturePlaylist.natureSounds.map(ns => ns.id));
    const hasInvalidIds = natureSoundOrder.some(id => !naturePlaylistSoundIds.has(id));
    
    if (hasInvalidIds) {
      return c.json({ error: "Invalid nature sound IDs in order array" }, 400);
    }

    // Update the nature playlist with new nature sound order
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        natureSoundOrder
      },
      include: {
        natureSounds: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Remove a specific nature sound from playlist (Admin)
  .delete("/:id/nature-sounds/:natureSoundId", adminMiddleware, async (c) => {
    const naturePlaylistId = c.req.param("id");
    const natureSoundId = c.req.param("natureSoundId");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
      include: {
        natureSounds: {
          where: { id: natureSoundId },
          select: { id: true }
        }
      }
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Check if nature sound exists in playlist
    if (naturePlaylist.natureSounds.length === 0) {
      return c.json({ error: "Nature sound not found in nature playlist" }, 404);
    }

    // Get current natureSoundOrder and remove the natureSoundId
    const currentNatureSoundOrder = naturePlaylist.natureSoundOrder || [];
    const newNatureSoundOrder = currentNatureSoundOrder.filter(id => id !== natureSoundId);

    // Remove nature sound from playlist and update natureSoundOrder
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        natureSounds: {
          disconnect: { id: natureSoundId }
        },
        natureSoundOrder: newNatureSoundOrder
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Videos Management (Admin)
  .post("/:id/videos", adminMiddleware, zValidator("json", z.object({
    videoIds: z.array(z.string())
  })), async (c) => {
    const naturePlaylistId = c.req.param("id");
    const { videoIds } = c.req.valid("json");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Add videos to nature playlist
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        videos: {
          connect: videoIds.map(id => ({ id }))
        }
      },
      include: {
        videos: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Remove a specific video from nature playlist (Admin)
  .delete("/:id/videos/:videoId", adminMiddleware, async (c) => {
    const naturePlaylistId = c.req.param("id");
    const videoId = c.req.param("videoId");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
      include: {
        videos: {
          where: { id: videoId },
          select: { id: true }
        }
      }
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Check if video exists in playlist
    if (naturePlaylist.videos.length === 0) {
      return c.json({ error: "Video not found in nature playlist" }, 404);
    }

    // Remove video from nature playlist
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        videos: {
          disconnect: { id: videoId }
        }
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  });

export default app; 