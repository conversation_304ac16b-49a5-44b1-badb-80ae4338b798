// Music.prisma
model Music {
    id          String       @id @default(cuid())
    title       String /// @zod.custom.use(z.string().min(1))
    src         String? /// @zod.custom.use(z.string().url())
    source      MediaSource? @default(OTHER)
    rating      Float? /// @zod.custom.use(z.number().min(0).max(5))
    isPublic    Boolean      @default(false)
    isCopyright Boolean      @default(false)
    creatorType UserRole     @default(admin) // To distinguish admin vs user created content

    // Relationship with genres (many-to-many)
    genres MusicGenre[]

    duration Int? @default(0) // Default to 3 minutes

    note String? @db.Text

    useAsOpeningMusic Boolean @default(false)

    // User who created this music track (optional)
    userId String
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    // Relationship with music playlists
    musicPlaylists     MusicPlaylist[]
    musicPlaylistsUser MusicPlaylistUser[]

    createdAt DateTime @default(now()) /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))
    updatedAt DateTime @updatedAt /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))

    @@index([userId])
}

enum MusicGenre {
    PIANO
    GUITAR
    RELAX
    HAPPY
    MEDITATION
    LOFI
    SAD
    JOYFUL
    CALM
    ENERGETIC
    ACOUSTIC
    COUNTRY
    ELECTRONIC
    INDIE
    AMBIENT
    CHILL
    STUDY
    MYSTERIOUS
    MOTIVATIONAL
}

enum MediaSource {
    EPIDEMICSOUND
    PIXABAY
    SUNO
    YOUTUBE
    OTHER
}
