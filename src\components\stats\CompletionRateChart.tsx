"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { useState } from "react"

interface CompletionRateChartProps {
  data: {
    completed: number
    interrupted: number
  }
  /**
   * Optional label describing the time period the data covers (e.g. "Last 30 days").
   * Defaults to "Last 30 days" if not provided.
   */
  periodLabel?: string
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ name: string; value: number; payload: { color: string } }>
}

export function CompletionRateChart({ data, periodLabel = "Last 30 days" }: CompletionRateChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | undefined>(undefined)

  // Map data to colors from the design system variables for visual consistency
  // Resolve CSS custom properties to real color strings because SVG `fill` sometimes
  // fails to interpret `var(--color)` directly, especially for OKLCH values.
  const getCssVar = (cssVar: string, fallback: string) => {
    if (typeof window === "undefined") return fallback
    const value = getComputedStyle(document.documentElement).getPropertyValue(cssVar).trim()
    // Many browsers do not yet support OKLCH, which our design tokens use. Fall back
    // to a safe colour if the returned string contains the "oklch(" syntax or is empty.
    if (!value || value.startsWith("oklch")) {
      return fallback
    }
    return value
  }

  const chartData = [
    {
      name: "Completed",
      value: data.completed,
      color: getCssVar("--chart-2", "#16a34a") // fallback: green-600
    },
    {
      name: "Interrupted",
      value: data.interrupted,
      color: getCssVar("--chart-4", "#f97316") // fallback: orange-500
    }
  ]

  const totalSessions = data.completed + data.interrupted
  const completionRate = totalSessions > 0 ? Math.round((data.completed / totalSessions) * 100) : 0

  // Render percentage labels on each slice
  const renderLabel = (props: any) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, percent } = props
    if (percent === 0) return null

    const RADIAN = Math.PI / 180
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return (
      <text
        x={x}
        y={y}
        style={{ fill: getCssVar("--foreground", "#334155") }}
        textAnchor="middle"
        dominantBaseline="central"
        fontSize={11}
        fontWeight={600}
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    )
  }

  // Custom tooltip with improved styling and data clarity
  const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const entry = payload[0]
      const percent = totalSessions > 0 ? Math.round((entry.value / totalSessions) * 100) : 0
      return (
        <div className="rounded-lg border border-border bg-card backdrop-blur-sm p-3 shadow-xl" role="tooltip">
          <p className="mb-1 font-medium text-foreground">{entry.name}</p>
          <p style={{ color: entry.payload.color }}>
            <span className="font-bold">{entry.value}</span> sessions ({percent}%)
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div
      className="flex flex-col items-center justify-center space-y-4"
      role="img"
      aria-label={`Completion rate chart showing ${completionRate}% completed and ${100 - completionRate}% interrupted sessions`}
    >
      {/* Summary */}
      <div className="text-center">
        <p className="text-sm text-muted-foreground">Completion Rate</p>
        <p className="text-3xl font-extrabold text-chart-2">{completionRate}%</p>
        <p className="text-xs text-muted-foreground mt-1">{periodLabel}</p>
      </div>

      {/* Pie Chart */}
      <div className="h-[220px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={80}
              paddingAngle={4}
              dataKey="value"
              labelLine={false}
              label={renderLabel}
              activeIndex={activeIndex}
              onMouseEnter={(_, index) => setActiveIndex(index)}
              onMouseLeave={() => setActiveIndex(undefined)}
              isAnimationActive
              animationDuration={900}
              animationEasing="ease-out"
            >
              {chartData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.color}
                  tabIndex={0}
                  aria-label={`${entry.name}: ${entry.value} sessions`}
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Legend */}
      <div
        className="flex flex-wrap justify-center gap-4"
        role="list"
        aria-label="Legend for completed versus interrupted sessions"
      >
        {chartData.map((entry) => {
          const percent = totalSessions > 0 ? Math.round((entry.value / totalSessions) * 100) : 0
          return (
            <div
              key={entry.name}
              className="flex items-center text-xs text-muted-foreground"
              tabIndex={0}
              role="listitem"
              aria-label={`${entry.name}: ${entry.value} sessions (${percent}%)`}
            >
              <div
                className="mr-2 h-3 w-3 rounded-full"
                style={{ backgroundColor: entry.color }}
                aria-hidden="true"
              />
              <span className="whitespace-nowrap">
                {entry.name} — {percent}%
              </span>
            </div>
          )
        })}
      </div>
    </div>
  )
}
