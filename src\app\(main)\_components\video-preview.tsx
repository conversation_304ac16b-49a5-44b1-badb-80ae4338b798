'use client';

import { useRef, useState, useEffect, useCallback, memo, useMemo } from 'react';
import { Video, AlertCircle, ListVideo } from 'lucide-react';
import Image from 'next/image';
import { Video as VideoType } from '@/lib/pomodoro-store';
import { cn } from '@/lib/utils';
import { SyntheticEvent } from 'react';
import { ErrorBoundary } from '@/components/error-boundary';
import {
  getDeviceInfo,
  safeVideoPlay,
  safeVideoCleanup,
  preloadImage
} from '@/lib/ios-safe-video';

interface VideoPreviewProps {
  video: VideoType | null;
  className?: string;
}

function VideoPreviewComponent({ video, className }: VideoPreviewProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isThumbnailLoading, setIsThumbnailLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [currentThumbnail, setCurrentThumbnail] = useState<string | null>(null);
  const [prevVideo, setPrevVideo] = useState<VideoType | null>(null);
  const [transitionPhase, setTransitionPhase] = useState<'initial' | 'loading' | 'crossfade' | 'complete'>('initial');
  const [isHovered, setIsHovered] = useState(false);
  const [deviceInfo] = useState(() => getDeviceInfo());

  // Stable cleanup function for video element
  const cleanupVideo = useCallback(() => {
    if (videoRef.current) {
      try {
        safeVideoCleanup(videoRef.current);
      } catch (error) {
        console.warn('Error during video cleanup:', error);
      }
    }
  }, []);

  // Memoize video change detection
  const hasVideoChanged = useCallback((currentVideo: VideoType | null, previousVideo: VideoType | null): boolean => {
    return Boolean(currentVideo && (!previousVideo || currentVideo.id !== previousVideo.id));
  }, []);

  // Handle thumbnail loading with optimized performance
  const handleThumbnailLoad = useCallback(() => {
    try {
      setIsThumbnailLoading(false);

      // Use requestAnimationFrame for smoother transitions
      // This defers the state update until the browser is ready to paint
      // which helps prevent blocking the main thread
      const rafCallback = () => {
        try {
          setTransitionPhase('loading');
        } catch (error) {
          console.warn('Error setting transition phase:', error);
        }
      };

      if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(rafCallback);
      } else {
        // Fallback for environments without requestAnimationFrame
        setTimeout(rafCallback, 16);
      }
    } catch (error) {
      console.warn('Error in handleThumbnailLoad:', error);
      setIsError(true);
    }
  }, []);

  // Scroll to video grid section - memoized
  const scrollToVideoGridSection = useCallback(() => {
    const videoGridSection = document.querySelector('[data-section="video-grid-section"]');
    if (videoGridSection) {
      videoGridSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }, []);

  // Mouse event handlers - memoized
  const handleMouseEnter = useCallback(() => setIsHovered(true), []);
  const handleMouseLeave = useCallback(() => setIsHovered(false), []);

  // Video event handlers
  const handleCanPlay = useCallback(async () => {
    if (!videoRef.current) return;

    const video = videoRef.current;

    try {
      // Set volume before playing (only if not muted)
      if (!video.muted && !deviceInfo.isIOS) {
        video.volume = 0.2;
      }

      // Use iOS-safe video play
      const playSuccess = await safeVideoPlay(video, {
        fallbackToMuted: true,
        maxRetries: deviceInfo.isIOS ? 3 : 2
      });

      if (!playSuccess) {
        console.warn('Video play failed after all retries');
        setIsError(true);
        return;
      }

      // Small delay to ensure video is playing before showing it
      const rafCallback = () => {
        try {
          setTransitionPhase('complete');
        } catch (error) {
          console.warn('Error setting transition phase to complete:', error);
        }
      };

      if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(() => requestAnimationFrame(rafCallback));
      } else {
        setTimeout(rafCallback, 32);
      }
    } catch (error) {
      console.error('Error in handleCanPlay:', error);
      setIsError(true);
    }
  }, [deviceInfo.isIOS]);

  const handleVideoError = useCallback((event: SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('Video error event:', event);
    setIsError(true);
    cleanupVideo();
  }, [cleanupVideo]);

  // Optimized thumbnail and video loading logic
  useEffect(() => {
    // If video changed
    if (hasVideoChanged(video, prevVideo)) {
      // Reset states
      setIsThumbnailLoading(true);
      setIsError(false);
      setTransitionPhase('initial');

      // Keep the current thumbnail visible during transition
      setCurrentThumbnail(prevVideo?.thumbnail || video?.thumbnail || null);

      let isActive = true; // Flag for cleanup

      // Start loading the video immediately but with low priority
      if (video) {
        const loadVideo = async () => {
          if (!isActive) return;

          try {
            // Set previous video reference
            setPrevVideo(video);
            setCurrentThumbnail(video.thumbnail);

            // Preload the thumbnail using iOS-safe method
            await preloadImage(video.thumbnail);

            if (!isActive) return;

            setIsThumbnailLoading(false);
            setTransitionPhase('loading');

            // After thumbnail is loaded, start loading the video
            if (videoRef.current) {
              // Use iOS-safe video configuration
              videoRef.current.src = video.src;
              videoRef.current.playsInline = true;
              videoRef.current.preload = deviceInfo.isIOS ? 'metadata' : 'auto';

              // On iOS, mute by default for autoplay compatibility
              if (deviceInfo.isIOS) {
                videoRef.current.muted = true;
              }

              try {
                videoRef.current.load();
              } catch (e) {
                console.error('Error loading video:', e);
                setIsError(true);
              }
            }
          } catch (error) {
            if (!isActive) return;
            console.error('Error in video loading process:', error);
            setIsError(true);
            setIsThumbnailLoading(false);
          }
        };

        // Use requestIdleCallback for non-blocking loading when browser is idle
        const requestIdleCallbackPolyfill =
          (typeof window !== 'undefined' && window.requestIdleCallback) ||
          ((cb) => setTimeout(cb, 1));

        requestIdleCallbackPolyfill(loadVideo);
      }

      return () => {
        isActive = false;
      };
    }
  }, [video, prevVideo, hasVideoChanged, deviceInfo.isIOS]);

  // Clean up on unmount or when video changes
  useEffect(() => {
    return () => {
      cleanupVideo();
    };
  }, [cleanupVideo]);

  // Non-blocking video loading on mount or when video changes
  useEffect(() => {
    if (video && videoRef.current && videoRef.current.src !== video.src) {
      // Use requestAnimationFrame to defer video loading to next paint
      // This prevents blocking the main thread during initial render
      requestAnimationFrame(() => {
        if (!videoRef.current) return;

        // Only load if src is different to avoid unnecessary reloads
        const videoElement = videoRef.current;

        // Set video attributes for better performance
        videoElement.preload = 'metadata'; // Only load metadata initially
        videoElement.src = video.src;

        try {
          // Use setTimeout to further delay full loading until after critical rendering
          setTimeout(() => {
            if (videoElement) {
              videoElement.load();
            }
          }, 100);
        } catch (e) {
          console.error('Error in non-blocking video loading:', e);
          setIsError(true);
        }
      });
    }
  }, [video]);

  // Memoize UI elements for no-video state
  const emptyStateUI = useMemo(() => (
    <div
      className={cn(
        "bg-gradient-to-br from-muted/50 to-muted/30 border border-slate-200/40 dark:border-slate-800/40 rounded-lg h-full flex items-center justify-center",
        "transition-all duration-300 ease-in-out", // Faster transition
        "relative",
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="text-center p-6">
        <Video className="w-10 h-10 mx-auto mb-3 text-slate-600/70 dark:text-slate-300/70" />
        <p className="text-sm text-slate-700 dark:text-slate-200 font-medium" style={{
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.3)'
        }}>Select a background video below</p>
      </div>

      {/* Choose background button */}
      <div
        className={cn(
          "absolute inset-0 flex items-center justify-center",
          "z-20",
          "pointer-events-none",
          isHovered ? "opacity-100" : "opacity-0",
          "transition-opacity duration-150 ease-out will-change-[opacity]"
        )}
      >
        <button
          onClick={scrollToVideoGridSection}
          className={cn(
            // Glass morphism background with improved contrast
            "bg-black/30 hover:bg-black/40 backdrop-blur-xl",
            // Enhanced border with subtle gradient effect
            "border border-slate-/40 hover:border-slate-300/60",
            // Improved shadow for depth
            "shadow-lg hover:shadow-xl shadow-black/25",
            // Text styling for maximum readability with enhanced contrast
            "text-slate-100 font-medium",
            // Padding and shape
            "py-2 px-4 rounded-xl",
            // Layout
            "flex items-center gap-2.5",
            // Interactions and animations
            "transform-gpu transition-all duration-200 ease-out",
            "hover:scale-105 active:scale-95",
            "focus:outline-none",
            // Accessibility and interactions
            "pointer-events-auto cursor-pointer",
            // Performance optimizations
            "will-change-transform"
          )}
          style={{
            textShadow: '0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4)'
          }}
        >
          <ListVideo className="w-4 h-4" />
          <span className="text-sm tracking-wide">Choose Background</span>
        </button>
      </div>
    </div>
  ), [className, handleMouseEnter, handleMouseLeave, isHovered, scrollToVideoGridSection]);

  // Memoize the action button
  const actionButton = useMemo(() => (
    <div
      className={cn(
        "absolute inset-0 flex items-center justify-center",
        "z-20",
        "pointer-events-none",
        isHovered ? "opacity-100" : "opacity-0",
        "transition-opacity duration-150 ease-out will-change-[opacity]"
      )}
    >
      <button
        onClick={scrollToVideoGridSection}
        className={cn(
          // Glass morphism background with improved contrast
          "bg-black/40 hover:bg-black/50 backdrop-blur-xl",
          // Enhanced border with subtle gradient effect
          "border border-slate-300/40 hover:border-slate-300/60",
          // Improved shadow for depth
          "shadow-lg hover:shadow-xl shadow-black/25",
          // Text styling for maximum readability with enhanced contrast
          "text-slate-100 font-medium",
          // Padding and shape
          "py-2 px-4 rounded-xl",
          // Layout
          "flex items-center gap-2.5",
          // Interactions and animations
          "transform-gpu transition-all duration-200 ease-out",
          "hover:scale-105 active:scale-95",
          "focus:outline-none",
          // Accessibility and interactions
          "pointer-events-auto cursor-pointer",
          // Performance optimizations
          "will-change-transform"
        )}
        style={{
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4)'
        }}
      >
        <ListVideo className="w-4 h-4" />
        <span className="text-sm tracking-wide">Change Background</span>
      </button>
    </div>
  ), [isHovered, scrollToVideoGridSection]);

  // Memoize the video-loaded view
  const videoLoadedView = useMemo(() => {
    // console.log('Rendering video loaded view, video:', video?.id, 'source:', video?.src);
    return (
    <div
      className={cn(
        "relative rounded-lg overflow-hidden h-full",
        "border border-slate-200/40 dark:border-slate-800/40",
        "transition-all duration-300 ease-in-out", // Faster transition
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Persistent thumbnail layer - always render when available */}
      {currentThumbnail && (
        <div className={cn(
          "absolute inset-0 w-full h-full z-10",
          "transition-opacity duration-300 ease-in-out",
          transitionPhase === 'complete' ? 'opacity-0' : 'opacity-100',
          "will-change-[opacity]" // Hint for browser optimization
        )}>
          <Image
            src={currentThumbnail}
            alt={video?.title || "Background video"}
            className="w-full h-full object-cover"
            fill
            priority
            fetchPriority="high"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            loading="eager"
            unoptimized={false} // Let Next.js optimize the image
            onLoad={handleThumbnailLoad}
          />
        </div>
      )}

      {/* Optimized loading indicator - with reduced visual effects to prevent blocking */}
      {isThumbnailLoading && !isError && (
        <div className={cn(
          "absolute inset-0 flex items-center justify-center",
          "bg-black/10 dark:bg-black/30",
          "transition-opacity duration-200",
          "animate-in fade-in duration-200"
        )}>
          <div className="flex flex-col items-center gap-2 will-change-transform">
            {/* Use CSS-only spinner with reduced complexity */}
            <div className="w-8 h-8 border-2 border-muted-foreground/30 border-t-primary rounded-full animate-spin" />
            <span
              className="text-xs font-medium text-slate-200"
              style={{
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4)'
              }}
            >Loading...</span>
          </div>
        </div>
      )}

      {/* Optimized error state overlay - with reduced visual effects */}
      {isError && (
        <div className={cn(
          "absolute inset-0 flex items-center justify-center",
          "bg-muted/30", // Removed backdrop-blur for better performance
          "transition-opacity duration-200 animate-in fade-in"
        )}>
          <div className="bg-black/40 backdrop-blur-xl px-4 py-3 rounded-lg flex items-center gap-2 shadow-lg border border-red-400/30">
            <AlertCircle className="text-red-400 h-5 w-5" />
            <span
              className="text-sm font-medium text-slate-100"
              style={{
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4)'
              }}
            >Video could not be loaded</span>
          </div>
        </div>
      )}

      {/* Video container */}
      <div className="relative w-full h-full">
        <video
          ref={videoRef}
          className={cn(
            "w-full h-full object-cover absolute inset-0",
            deviceInfo.isIOS ? "" : "will-change-transform", // Avoid will-change on iOS for better performance
            deviceInfo.isIOS ? "" : "translate-z-0" // Avoid GPU acceleration hints on iOS
          )}
          autoPlay={false} // We handle playback manually
          loop
          playsInline={true} // Essential for iOS
          preload={deviceInfo.isIOS ? "metadata" : "auto"} // Use metadata on iOS to prevent blocking
          onCanPlay={handleCanPlay}
          onError={handleVideoError}
          muted={deviceInfo.isIOS} // Mute by default on iOS for autoplay compatibility
          controls={false}
          webkit-playsinline="true" // Legacy iOS support
          // Add data attributes for better performance
          data-fetchpriority="low"
          data-poster={video?.thumbnail || ''}
        />
      </div>

      {/* Title with improved animation */}
      {video && (
        <div className={cn(
          "absolute top-3 right-3",
          // Glass morphism background with improved contrast
          "bg-black/35 backdrop-blur-xl",
          // Enhanced border with subtle gradient effect
          "border border-slate-300/40",
          // Improved shadow for depth
          "shadow-lg shadow-black/25",
          // Padding and shape
          "px-3 py-1.5 rounded-xl",
          // Text styling for maximum readability
          "text-sm font-medium",
          // Interactions and animations
          "transition-all duration-300 ease-out transform will-change-transform",
          transitionPhase === 'initial' ? "translate-y-[-10px] opacity-0" :
          "translate-y-0 opacity-100 translate-z-0"
        )}>
          <span
            className="text-slate-100"
            style={{
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4)'
            }}
          >{video.title}</span>
        </div>
      )}

      {/* Choose background button */}
      {actionButton}
    </div>
    );
  }, [
    className,
    handleMouseEnter,
    handleMouseLeave,
    currentThumbnail,
    transitionPhase,
    handleThumbnailLoad,
    isThumbnailLoading,
    isError,
    handleCanPlay,
    handleVideoError,
    video,
    actionButton, deviceInfo.isIOS
  ]);

  // Return the appropriate UI based on video state
  return video ? videoLoadedView : emptyStateUI;
}

// Memoize the component to prevent unnecessary re-renders
const MemoizedVideoPreview = memo(VideoPreviewComponent);

// Wrap with error boundary for iOS safety
export const VideoPreview = ({ video, className }: VideoPreviewProps) => {
  return (
    <ErrorBoundary
      fallback={
        <div className={cn(
          "bg-gradient-to-br from-muted/50 to-muted/30 border border-slate-200/40 dark:border-slate-800/40 rounded-lg h-full flex items-center justify-center",
          className
        )}>
          <div className="text-center p-6">
            <AlertCircle className="w-10 h-10 mx-auto mb-3 text-red-500" />
            <p
              className="text-sm text-slate-700 dark:text-slate-200 font-medium"
              style={{
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.3)'
              }}
            >
              Video preview failed to load
            </p>
            <p
              className="text-xs text-slate-600 dark:text-slate-300 mt-1"
              style={{
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)'
              }}
            >
              This might be due to browser compatibility issues
            </p>
          </div>
        </div>
      }
      onError={(error, errorInfo) => {
        console.error('VideoPreview error:', {
          error: error.message,
          componentStack: errorInfo.componentStack,
          videoId: video?.id,
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown'
        });
      }}
    >
      <MemoizedVideoPreview video={video} className={className} />
    </ErrorBoundary>
  );
};