import { <PERSON><PERSON> } from "hono";
import { z<PERSON><PERSON>da<PERSON> } from "@hono/zod-validator";
import { createPomodoroSessionSchema, updatePomodoroSessionSchema, MINIMUM_FOCUS_DURATION_SECONDS, MINIMUM_BREAK_DURATION_SECONDS } from "./pomodoro-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { adminMiddleware, privateRoutesMiddleware } from "@/server/private/middleware";
import { Prisma } from "@prisma/client";
import { z } from "zod";
import { getTaskMapping, clearTaskMapping } from "../Tasks/task-route";

// Schema for bulk transfer
const bulkTransferSessionSchema = z.object({
  sessions: z.array(z.object({
    startTime: z.string(),
    endTime: z.string().nullable(),
    totalDuration: z.string(),
    focusDuration: z.string().optional(),
    breakDuration: z.string().optional(),
    intervalType: z.enum(['FOCUS', 'SHORT_BREAK', 'LONG_BREAK']),
    completed: z.string(),
    interrupted: z.string(),
    note: z.string().optional(),
    interruptedSessions: z.string().optional(),
    localTaskId: z.string().optional(),
  }))
});

const app = new Hono<{ Variables: UserVariable }>()
  // Get all pomodoro sessions for the current user
  .get("/", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");

    // Can filter by task
    const taskId = c.req.query("taskId");
    const isCompleted = c.req.query("completed") === "true" ? true : c.req.query("completed") === "false" ? false : undefined;
    const intervalType = c.req.query("intervalType") as "FOCUS" | "SHORT_BREAK" | "LONG_BREAK" | undefined;

    const filters: Record<string, unknown> = {
      userId: user.id
    };

    if (taskId) {
      filters.taskId = taskId;
    }

    if (isCompleted !== undefined) {
      filters.completed = isCompleted;
    }

    if (intervalType) {
      filters.intervalType = intervalType;
    }

    // Get pomodoro sessions sorted by start time descending (most recent first)
    const pomodoroSessions = await prisma.pomodoroSession.findMany({
      where: filters,
      include: {
        task: true,
      },
      orderBy: {
        startTime: 'desc',
      },
    });

    return c.json({ data: pomodoroSessions });
  })

  // Quick stats for popover (lightweight, optimized for quick display)
  .get("/quick-stats", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");

    // Get date parameters from frontend (calculated in user's timezone)
    const todayStart = c.req.query("todayStart");
    const todayEnd = c.req.query("todayEnd");
    const weekStart = c.req.query("weekStart");
    const weekEnd = c.req.query("weekEnd");
    const monthStart = c.req.query("monthStart");
    const monthEnd = c.req.query("monthEnd");

    // Fallback to server-side calculation if parameters not provided (backward compatibility)
    const now = new Date();
    const defaultToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const defaultTomorrow = new Date(defaultToday);
    defaultTomorrow.setDate(defaultTomorrow.getDate() + 1);

    const defaultWeekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    defaultWeekStart.setDate(defaultWeekStart.getDate() - 7);

    const defaultMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const defaultMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    // Use provided dates or fallback to defaults
    const todayStartDate = todayStart ? new Date(todayStart) : defaultToday;
    const todayEndDate = todayEnd ? new Date(todayEnd) : defaultTomorrow;
    const weekStartDate = weekStart ? new Date(weekStart) : defaultWeekStart;
    const weekEndDate = weekEnd ? new Date(weekEnd) : defaultTomorrow;
    const monthStartDate = monthStart ? new Date(monthStart) : defaultMonthStart;
    const monthEndDate = monthEnd ? new Date(monthEnd) : defaultMonthEnd;

    // Get today's sessions only
    const todaySessions = await prisma.pomodoroSession.findMany({
      where: {
        userId: user.id,
        startTime: {
          gte: todayStartDate,
          lt: todayEndDate
        },
      },
      include: {
        task: true,
      },
      orderBy: {
        startTime: 'desc',
      },
    });

    // Get week's sessions for totals
    const weekSessions = await prisma.pomodoroSession.findMany({
      where: {
        userId: user.id,
        startTime: {
          gte: weekStartDate,
          lte: weekEndDate
        },
      },
    });

    // Get month's sessions for monthly total
    const monthSessions = await prisma.pomodoroSession.findMany({
      where: {
        userId: user.id,
        startTime: {
          gte: monthStartDate,
          lte: monthEndDate
        },
      },
    });

    // Calculate today's focus time (only focus sessions)
    const todayFocusSessions = todaySessions.filter(s => s.intervalType === 'FOCUS');
    const todayFocusTime = todayFocusSessions.reduce((sum, s) => {
      const duration = s.focusDuration || s.totalDuration;
      return sum + Math.round(duration / 60); // Convert to minutes, use consistent rounding
    }, 0);

    // Calculate today's completed focus sessions count
    const todayCompletedSessions = todayFocusSessions.filter(s => s.completed).length;

    // Calculate week totals (focus sessions only)
    const weekFocusSessions = weekSessions.filter(s => s.intervalType === 'FOCUS');
    const weekTotalSessions = weekFocusSessions.length;
    const weekCompletedSessions = weekFocusSessions.filter(s => s.completed).length;
    
    // Calculate week focus time in minutes (consistent with today calculation)
    const weekFocusTime = weekFocusSessions.reduce((sum, s) => {
      const duration = s.focusDuration || s.totalDuration;
      return sum + Math.round(duration / 60); // Convert to minutes, consistent with today
    }, 0);

    // Calculate monthly total (focus sessions only)
    const monthFocusSessions = monthSessions.filter(s => s.intervalType === 'FOCUS');
    const monthlyTotal = monthFocusSessions.reduce((sum, s) => {
      const duration = s.focusDuration || s.totalDuration;
      return sum + Math.round(duration / 60); // Convert to minutes, consistent with other calculations
    }, 0);

    // Format today's sessions for display
    const formattedTodaySessions = todaySessions.map(session => {
      const startTime = new Date(session.startTime);
      const endTime = session.endTime ? new Date(session.endTime) : new Date(startTime.getTime() + session.totalDuration * 1000);

      // Use the appropriate duration field based on session type
      let activeDuration = session.totalDuration; // fallback
      if (session.intervalType === 'FOCUS' && session.focusDuration !== null) {
        activeDuration = session.focusDuration;
      } else if ((session.intervalType === 'SHORT_BREAK' || session.intervalType === 'LONG_BREAK') && session.breakDuration !== null) {
        activeDuration = session.breakDuration;
      }

      return {
        id: session.id,
        title: session.task?.title || `${session.intervalType.charAt(0) + session.intervalType.slice(1).toLowerCase()} Session`,
        duration: Math.round(activeDuration / 60), // Convert to minutes, use round for consistency with sessionsByDay
        completed: session.completed,
        interrupted: session.interrupted,
        timeRange: `${startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`,
        type: session.intervalType.toLowerCase(),
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString()
      };
    });

    return c.json({
      data: {
        // Today's metrics
        todayFocusTime, // in minutes
        todayCompletedSessions,
        todaySessions: formattedTodaySessions,

        // Week totals - now consistent with today metrics (both in minutes)
        weekTotalDuration: weekFocusTime, // in minutes (changed from hours for consistency)

        // Monthly total - replaces weekCompletionRate
        monthlyTotal, // in minutes

        // Metadata
        dateRange: {
          today: todayStartDate.toISOString().split('T')[0],
          weekStart: weekStartDate.toISOString().split('T')[0],
          monthStart: monthStartDate.toISOString().split('T')[0],
          monthEnd: monthEndDate.toISOString().split('T')[0]
        }
      }
    });
  })

  // Get sessions for a specific date (for timeline component)
  .get("/date-sessions", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const dateParam = c.req.query("date");

    if (!dateParam) {
      return c.json({ error: "Date parameter is required" }, 400);
    }

    // Parse the date and create start/end boundaries in user's timezone
    // The frontend should send dates in YYYY-MM-DD format
    const targetDate = new Date(dateParam + 'T00:00:00.000Z');
    const startOfDay = new Date(targetDate);
    const endOfDay = new Date(targetDate);
    endOfDay.setDate(endOfDay.getDate() + 1);

    // Get sessions for the specific date
    const sessions = await prisma.pomodoroSession.findMany({
      where: {
        userId: user.id,
        startTime: {
          gte: startOfDay,
          lt: endOfDay
        },
      },
      include: {
        task: true,
      },
      orderBy: {
        startTime: 'asc',
      },
    });

    // Format sessions for the timeline component
    const formattedSessions = sessions.map(session => {
      const startTime = new Date(session.startTime);
      const endTime = session.endTime ? new Date(session.endTime) : new Date(session.startTime.getTime() + (session.totalDuration * 1000));

      // Format time range
      const timeRange = `${startTime.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })} - ${endTime.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })}`;

      return {
        id: session.id,
        title: session.task?.title || `${session.intervalType.toLowerCase().replace('_', ' ')} Session`,
        duration: Math.round((session.focusDuration || session.totalDuration) / 60), // Convert to minutes
        completed: session.completed,
        interrupted: session.interrupted,
        timeRange,
        type: session.intervalType.toLowerCase().replace('_', ''),
        startTime: session.startTime.toISOString(),
        endTime: endTime.toISOString(),
      };
    });

    return c.json({
      data: {
        sessions: formattedSessions,
        date: dateParam
      }
    });
  })

    // Get comprehensive user statistics for dashboard (detailed analytics)
    .get("/stats", privateRoutesMiddleware, async (c) => {
      const user = c.get("user");

      // Helper function to format date consistently without timezone issues
      const formatDateString = (date: Date): string => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      const timeRangeInDays = Number(c.req.query("days") || 7);
      const specificDate = c.req.query("date"); // Add support for specific date query
      const startDateParam = c.req.query("startDate"); // Add support for custom date range
      const endDateParam = c.req.query("endDate"); // Add support for custom date range

      // Calculate date range for statistics
      let startDate = new Date();
      let endDate = new Date(); // Default to current time

      if (specificDate) {
        // If specific date is provided, get stats for just that day
        const date = new Date(specificDate);
        startDate.setTime(date.getTime());
        startDate.setHours(0, 0, 0, 0); // Start of day

        endDate = new Date(date);
        endDate.setHours(23, 59, 59, 999); // End of day
      } else if (startDateParam && endDateParam) {
        // If custom date range is provided
        startDate = new Date(startDateParam);
        startDate.setHours(0, 0, 0, 0); // Start of day

        endDate = new Date(endDateParam);
        endDate.setHours(23, 59, 59, 999); // End of day
      } else {
        // Otherwise use the days parameter for a range
        startDate.setDate(startDate.getDate() - timeRangeInDays);
      }

      // Get sessions completed within the date range
      const sessions = await prisma.pomodoroSession.findMany({
        where: {
          userId: user.id,
          startTime: {
            gte: startDate,
            lte: endDate
          },
        },
        include: {
          task: true,
        },
        orderBy: {
          startTime: 'desc',
        },
      });

      // Calculate metrics
      const totalSessions = sessions.length;
      const completedSessions = sessions.filter(s => s.completed && s.intervalType === 'FOCUS').length; // Only count completed focus sessions
      const interruptedSessions = sessions.filter(s => s.interrupted && s.intervalType === 'FOCUS').length; // Only count interrupted focus sessions
      const totalDuration = sessions.reduce((sum, s) => sum + s.totalDuration, 0);

      // Calculate session type breakdowns
      const focusSessions = sessions.filter(s => s.intervalType === 'FOCUS').length;
      const shortBreakSessions = sessions.filter(s => s.intervalType === 'SHORT_BREAK').length;
      const longBreakSessions = sessions.filter(s => s.intervalType === 'LONG_BREAK').length;

      // Calculate duration by session type
      const focusDuration = sessions
        .filter(s => s.intervalType === 'FOCUS')
        .reduce((sum, s) => sum + (s.focusDuration || s.totalDuration), 0);

      const shortBreakDuration = sessions
        .filter(s => s.intervalType === 'SHORT_BREAK')
        .reduce((sum, s) => sum + (s.breakDuration || s.totalDuration), 0);

      const longBreakDuration = sessions
        .filter(s => s.intervalType === 'LONG_BREAK')
        .reduce((sum, s) => sum + (s.breakDuration || s.totalDuration), 0);

      // Group by day for daily stats with focus minutes
      const sessionsByDay: Record<string, { count: number, focusMinutes: number }> = {};

      sessions.forEach(session => {
        // Use local date for consistent grouping - avoid timezone conversion issues
        const sessionLocalDate = new Date(session.startTime);
        const dateStr = formatDateString(sessionLocalDate);

        if (!sessionsByDay[dateStr]) {
          sessionsByDay[dateStr] = { count: 0, focusMinutes: 0 };
        }

        // Only count focus sessions for the streak calendar
        if (session.intervalType === 'FOCUS') {
          sessionsByDay[dateStr].count += 1;

          // Use focusDuration if available, fallback to totalDuration, convert seconds to minutes
          const duration = session.focusDuration || session.totalDuration;
          const minutes = Math.round(duration / 60);

          sessionsByDay[dateStr].focusMinutes += minutes;
        }
      });

      // Group by hour for hourly distribution (using focus duration in minutes)
      const hourlyDistribution = Array(24).fill(0).map((_, index) => ({
        hour: index,
        value: 0
      }));

      sessions.forEach(session => {
        const hour = new Date(session.startTime).getHours();
        if (session.intervalType === 'FOCUS') {
          // Use focus duration if available, otherwise total duration, convert to minutes
          const duration = session.focusDuration || session.totalDuration;
          hourlyDistribution[hour].value += Math.round(duration / 60);
        }
      });

      // Get today's sessions - use consistent timezone handling
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // Local timezone
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const todaySessions = sessions.filter(session => {
        const sessionDate = new Date(session.startTime);
        return sessionDate >= today && sessionDate < tomorrow;
      }).map(session => {
        const startTime = new Date(session.startTime);
        const endTime = session.endTime ? new Date(session.endTime) : new Date(startTime.getTime() + session.totalDuration * 1000);

        // Use the appropriate duration field based on session type
        let activeDuration = session.totalDuration; // fallback
        if (session.intervalType === 'FOCUS' && session.focusDuration !== null) {
          activeDuration = session.focusDuration;
        } else if ((session.intervalType === 'SHORT_BREAK' || session.intervalType === 'LONG_BREAK') && session.breakDuration !== null) {
          activeDuration = session.breakDuration;
        }

        return {
          id: session.id,
          title: session.task?.title || `${session.intervalType.charAt(0) + session.intervalType.slice(1).toLowerCase()} Session`,
          duration: Math.round(activeDuration / 60), // Convert to minutes, use round for consistency with sessionsByDay
          completed: session.completed,
          interrupted: session.interrupted,
          timeRange: `${startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`,
          type: session.intervalType.toLowerCase(),
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString()
        };
      });

      // Calculate streak data (days with focus sessions only)
      const streakData: Array<{ date: string; count: number }> = [];
      Object.entries(sessionsByDay).forEach(([date, data]) => {
        streakData.push({
          date,
          count: data.count // This now only includes focus sessions
        });
      });

      // Calculate weekly comparison (this week vs last week) - use consistent timezone
      // First day of current week (Sunday = 0) - use local timezone
      const currentWeekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const currentDay = currentWeekStart.getDay();
      currentWeekStart.setDate(currentWeekStart.getDate() - currentDay);

      // First day of previous week
      const previousWeekStart = new Date(currentWeekStart);
      previousWeekStart.setDate(previousWeekStart.getDate() - 7);

      // End of previous week
      const previousWeekEnd = new Date(currentWeekStart);
      previousWeekEnd.setMilliseconds(-1);

      // Calculate weekly comparison data
      const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
      const weeklyComparison = daysOfWeek.map((name, index) => {
        const thisWeekDay = new Date(currentWeekStart);
        thisWeekDay.setDate(thisWeekDay.getDate() + index);
        const thisWeekKey = formatDateString(thisWeekDay);

        const lastWeekDay = new Date(previousWeekStart);
        lastWeekDay.setDate(lastWeekDay.getDate() + index);
        const lastWeekKey = formatDateString(lastWeekDay);

        const thisWeekMinutes = sessionsByDay[thisWeekKey]?.focusMinutes || 0;
        const lastWeekMinutes = sessionsByDay[lastWeekKey]?.focusMinutes || 0;

        return {
          name,
          thisWeek: thisWeekMinutes,
          lastWeek: lastWeekMinutes,
        };
      });

      return c.json({
        data: {
          totalSessions,
          completedSessions,
          interruptedSessions,
          totalDuration,
          completionRate: focusSessions > 0 ? (completedSessions / focusSessions) * 100 : 0, // Base completion rate on focus sessions only
          // Session type breakdown
          focusSessions,
          shortBreakSessions,
          longBreakSessions,
          // Duration by session type
          focusDuration,
          shortBreakDuration,
          longBreakDuration,
          // Daily stats
          sessionsByDay,
          // Advanced stats
          todaySessions,
          streakData,
          hourlyDistribution,
          weeklyComparison,
          dateRange: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          }
        }
      });
  })

  // Get single pomodoro session
  .get("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    const pomodoroSession = await prisma.pomodoroSession.findUnique({
      where: { id },
      include: {
        task: true,
      },
    });

    if (!pomodoroSession) {
      return c.json({ error: "Pomodoro session not found" }, 404);
    }

    // Check if user has permission to access this pomodoro session
    if (pomodoroSession.userId !== user.id) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    return c.json({ data: pomodoroSession });
  })

  // Create pomodoro session
  .post("/", privateRoutesMiddleware, zValidator("form", createPomodoroSessionSchema), async (c) => {
    const user = c.get("user");
    const data = c.req.valid("form");

    if (!user) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    // Additional server-side validation for minimum durations
    const validateMinimumDuration = (sessionData: typeof data): { valid: boolean; error?: string } => {
      // Get the actual duration to validate based on session type
      const getDurationToValidate = () => {
        if (sessionData.intervalType === "FOCUS") {
          return sessionData.focusDuration ?? sessionData.totalDuration;
        } else if (sessionData.intervalType === "SHORT_BREAK" || sessionData.intervalType === "LONG_BREAK") {
          return sessionData.breakDuration ?? sessionData.totalDuration;
        }
        return sessionData.totalDuration;
      };

      const duration = getDurationToValidate();
      const durationSeconds = typeof duration === 'string' ? parseInt(duration, 10) : duration;

      // Focus sessions must be at least the minimum duration
      if (sessionData.intervalType === "FOCUS" && durationSeconds < MINIMUM_FOCUS_DURATION_SECONDS) {
        return {
          valid: false,
          error: `Focus sessions must be at least ${MINIMUM_FOCUS_DURATION_SECONDS / 60} minutes (${MINIMUM_FOCUS_DURATION_SECONDS} seconds)`
        };
      }

      // Break sessions must be at least the minimum duration
      if ((sessionData.intervalType === "SHORT_BREAK" || sessionData.intervalType === "LONG_BREAK") && durationSeconds < MINIMUM_BREAK_DURATION_SECONDS) {
        return {
          valid: false,
          error: `Break sessions must be at least ${MINIMUM_BREAK_DURATION_SECONDS / 60} minute${MINIMUM_BREAK_DURATION_SECONDS > 60 ? 's' : ''} (${MINIMUM_BREAK_DURATION_SECONDS} seconds)`
        };
      }

      return { valid: true };
    };

    // Validate the session duration
    const validation = validateMinimumDuration(data);
    if (!validation.valid) {
      return c.json({ error: validation.error }, 400);
    }

    // Sanitize data for Prisma - handle JSON fields and null values properly
    const sanitizedData = {
      startTime: typeof data.startTime === 'string' ? new Date(data.startTime) : data.startTime,
      endTime: data.endTime ? (typeof data.endTime === 'string' ? new Date(data.endTime) : data.endTime) : null,
      totalDuration: typeof data.totalDuration === 'string'
        ? parseInt(data.totalDuration, 10)
        : data.totalDuration,
      focusDuration: data.focusDuration !== undefined && data.focusDuration !== null
        ? (typeof data.focusDuration === 'string' ? parseInt(data.focusDuration, 10) : data.focusDuration)
        : null,
      breakDuration: data.breakDuration !== undefined && data.breakDuration !== null
        ? (typeof data.breakDuration === 'string' ? parseInt(data.breakDuration, 10) : data.breakDuration)
        : null,
      intervalType: data.intervalType,
      completed: data.completed,
      interrupted: data.interrupted,
      note: data.note || null,
      interruptedSessions: data.interruptedSessions
        ? (data.interruptedSessions as unknown as Prisma.JsonValue)
        : Prisma.JsonNull,
      user: {
        connect: { id: user.id }
      },
      // Only connect to task if taskId is provided and not null/undefined
      ...(data.taskId && data.taskId !== 'undefined' && data.taskId !== 'null' && { task: { connect: { id: data.taskId } } }),
    } as Prisma.PomodoroSessionCreateInput;

    // Create pomodoro session
    const pomodoroSession = await prisma.pomodoroSession.create({
      data: sanitizedData,
      include: {
        task: true,
      },
    });

    return c.json({ data: pomodoroSession }, 201);
  })

  // Update pomodoro session
  .patch("/:id", privateRoutesMiddleware, zValidator("form", updatePomodoroSessionSchema), async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // First check if pomodoro session exists
    const existingSession = await prisma.pomodoroSession.findUnique({
      where: { id },
    });

    if (!existingSession) {
      return c.json({ error: "Pomodoro session not found" }, 404);
    }

    // Check if user has permission to update
    if (existingSession.userId !== user.id) {
      return c.json({ error: "Unauthorized to update this pomodoro session" }, 403);
    }

    // Sanitize updates for Prisma - handle proper null vs undefined for optional fields
    const sanitizedUpdates = {} as Prisma.PomodoroSessionUpdateInput;

    if (updates.startTime !== undefined) {
      sanitizedUpdates.startTime = updates.startTime;
    }

    if (updates.endTime !== undefined) {
      sanitizedUpdates.endTime = updates.endTime ? (typeof updates.endTime === 'string' ? new Date(updates.endTime) : updates.endTime) : null;
    }

    if (updates.totalDuration !== undefined) {
      sanitizedUpdates.totalDuration = updates.totalDuration;
    }

    if (updates.focusDuration !== undefined) {
      sanitizedUpdates.focusDuration = updates.focusDuration;
    }

    if (updates.breakDuration !== undefined) {
      sanitizedUpdates.breakDuration = updates.breakDuration;
    }

    if (updates.intervalType !== undefined) {
      sanitizedUpdates.intervalType = updates.intervalType;
    }

    if (updates.completed !== undefined) {
      sanitizedUpdates.completed = updates.completed;
    }

    if (updates.interrupted !== undefined) {
      sanitizedUpdates.interrupted = updates.interrupted;
    }

    if (updates.note !== undefined) {
      sanitizedUpdates.note = updates.note;
    }

    if (updates.interruptedSessions !== undefined) {
      (sanitizedUpdates as any).interruptedSessions = updates.interruptedSessions
        ? (updates.interruptedSessions as unknown as Prisma.JsonValue)
        : Prisma.JsonNull;
    }

    if (updates.taskId !== undefined) {
      if (updates.taskId === null || updates.taskId === 'undefined' || updates.taskId === 'null' || updates.taskId === '') {
        // Disconnect the task
        sanitizedUpdates.task = { disconnect: true };
      } else {
        // Connect to a new task
        sanitizedUpdates.task = { connect: { id: updates.taskId } };
      }
    }

    // Update the pomodoro session
    const updatedSession = await prisma.pomodoroSession.update({
      where: { id },
      data: sanitizedUpdates,
      include: {
        task: true,
      },
    });

    return c.json({ data: updatedSession });
  })

  // Delete pomodoro session
  .delete("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    // Find the pomodoro session
    const session = await prisma.pomodoroSession.findUnique({
      where: { id },
    });

    if (!session) {
      return c.json({ error: "Pomodoro session not found" }, 404);
    }

    // Check permissions
    if (session.userId !== user.id) {
      return c.json({ error: "Unauthorized to delete this pomodoro session" }, 403);
    }

    // Delete pomodoro session
    await prisma.pomodoroSession.delete({
      where: { id },
    });

    return c.json({ success: true });
  })

  // Bulk transfer local sessions to database
  .post("/bulk-transfer", privateRoutesMiddleware, zValidator("json", bulkTransferSessionSchema), async (c) => {
    const user = c.get("user");
    const { sessions } = c.req.valid("json");

    if (!user) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    // Get the task ID mapping stored during task transfer
    const taskMapping = getTaskMapping(user.id);

    let transferred = 0;
    let skipped = 0;

    for (const sessionData of sessions) {
      try {
        // Validate minimum durations (same validation as single session creation)
        const getDurationToValidate = () => {
          if (sessionData.intervalType === "FOCUS") {
            return sessionData.focusDuration ? parseInt(sessionData.focusDuration, 10) : parseInt(sessionData.totalDuration, 10);
          } else if (sessionData.intervalType === "SHORT_BREAK" || sessionData.intervalType === "LONG_BREAK") {
            return sessionData.breakDuration ? parseInt(sessionData.breakDuration, 10) : parseInt(sessionData.totalDuration, 10);
          }
          return parseInt(sessionData.totalDuration, 10);
        };

        const durationSeconds = getDurationToValidate();

        // Focus sessions must be at least the minimum duration
        if (sessionData.intervalType === "FOCUS" && durationSeconds < MINIMUM_FOCUS_DURATION_SECONDS) {
          skipped++;
          continue;
        }

        // Break sessions must be at least the minimum duration
        if ((sessionData.intervalType === "SHORT_BREAK" || sessionData.intervalType === "LONG_BREAK") && durationSeconds < MINIMUM_BREAK_DURATION_SECONDS) {
          skipped++;
          continue;
        }

        // Map local task ID to database task ID using stored mapping
        let mappedTaskId: string | undefined;
        if (sessionData.localTaskId && taskMapping) {
          mappedTaskId = taskMapping[sessionData.localTaskId];
          if (mappedTaskId) {
            console.log(`Mapped local task ID ${sessionData.localTaskId} to database task ID ${mappedTaskId}`);
          } else {
            console.warn(`No mapping found for local task ID: ${sessionData.localTaskId}`);
          }
        }

        // Prepare data for Prisma (same structure as single session creation)
        const sanitizedData = {
          startTime: new Date(sessionData.startTime),
          endTime: sessionData.endTime ? new Date(sessionData.endTime) : null,
          totalDuration: parseInt(sessionData.totalDuration, 10),
          focusDuration: sessionData.focusDuration ? parseInt(sessionData.focusDuration, 10) : null,
          breakDuration: sessionData.breakDuration ? parseInt(sessionData.breakDuration, 10) : null,
          intervalType: sessionData.intervalType,
          completed: sessionData.completed === 'true',
          interrupted: sessionData.interrupted === 'true',
          note: sessionData.note || null,
          interruptedSessions: sessionData.interruptedSessions
            ? (JSON.parse(sessionData.interruptedSessions) as Prisma.JsonValue)
            : Prisma.JsonNull,
          user: {
            connect: { id: user.id }
          },
          // Connect to task if we have a mapped task ID
          ...(mappedTaskId && { task: { connect: { id: mappedTaskId } } }),
        } as Prisma.PomodoroSessionCreateInput;

        // Create the session
        await prisma.pomodoroSession.create({
          data: sanitizedData,
        });

        transferred++;

      } catch (error) {
        console.error('Error transferring session:', error);
        skipped++;
      }
    }

    // Clear the task mapping after successful session transfer
    if (taskMapping) {
      clearTaskMapping(user.id);
      console.log(`Cleared task mapping for user ${user.id} after session transfer`);
    }

    return c.json({
      data: {
        transferred,
        skipped
      }
    });
  })

export default app;